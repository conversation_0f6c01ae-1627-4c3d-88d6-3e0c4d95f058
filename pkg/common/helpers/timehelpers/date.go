package timehelpers

import "time"

func ParseDateFilter(dateString string, isEndDate bool) (time.Time, error) {
	layout := "2006-01-02"

	parsedDate, err := time.ParseInLocation(layout, dateString, time.Local)
	if err != nil {
		return time.Time{}, err
	}

	if isEndDate {
		parsedDate = time.Date(
			parsedDate.Year(),
			parsedDate.Month(),
			parsedDate.Day(),
			23, 59, 59, 0, // hour, minute, second, nanosecond
			parsedDate.Location(),
		)
	}

	return parsedDate, nil
}
