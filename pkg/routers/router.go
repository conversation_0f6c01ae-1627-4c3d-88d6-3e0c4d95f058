package routers

import (
	"assetfindr/internal/constants"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/middleware"
	"bytes"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// LogrusWriter is an io.Writer that writes to a logrus logger
type LogrusWriter struct {
	logger *logrus.Logger
}

// NewLogrusWriter creates a new LogrusWriter
func NewLogrusWriter(logger *logrus.Logger) *LogrusWriter {
	return &LogrusWriter{logger: logger}
}

// Write writes data to the logrus logger
func (w *LogrusWriter) Write(p []byte) (n int, err error) {
	w.logger.Error(string(bytes.TrimSpace(p)))
	return len(p), nil
}

func SetupRoute() *gin.Engine {

	environment := viper.GetBool(constants.CONFIG_SERVER_DEBUG)
	if environment {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	allowedHosts := viper.GetString(constants.CONFIG_SERVER_ALLOWED_HOSTS)
	router := gin.New()
	router.SetTrustedProxies([]string{allowedHosts})
	// router.Use(gin.Logger())
	router.Use(gin.RecoveryWithWriter(NewLogrusWriter(commonlogger.GetLogger()), func(c *gin.Context, _ any) {
		c.JSON(http.StatusInternalServerError, commonmodel.DetailResponse{
			Success:     false,
			Message:     "Internal Server Error",
			ReferenceID: "",
			Data:        nil,
		})
	}))
	router.Use(middleware.CORSMiddleware())
	router.Use(middleware.LoggerMiddleware())

	router.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{"status": http.StatusNotFound, "message": "Route Not Found"})
	})
	router.GET("/health", func(c *gin.Context) { c.JSON(http.StatusOK, gin.H{"live": "ok"}) })

	return router
}
