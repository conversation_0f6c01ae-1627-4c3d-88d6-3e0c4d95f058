package assetvehiclerandom

import (
	"assetfindr/cmd/csvgenerator/randomize"
	"assetfindr/internal/app/asset/dtos"
	"math/rand"
	"time"

	"gopkg.in/guregu/null.v4"
)

// Function to generate random data
func GenerateRandomDataAssetVehicle() dtos.BulkUploadAssetVehicleReq {
	return dtos.BulkUploadAssetVehicleReq{
		ReferenceID:                           "",
		IsSuccess:                             false,
		IsValidToProcess:                      false,
		BrandNo:                               "BRD-GEN-13",
		BrandID:                               "",
		LocationNo:                            randomize.RandomString(8),
		AssetVehicleBodyTypeNo:                "VBT-GEN-4",
		AssetVehicleBodyTypeCode:              "",
		RegistrationCertificateAssignToEmail:  "<EMAIL>",
		RegistrationCertificateAssignToUserID: "",
		InspectionBookNumberAssignToEmail:     "<EMAIL>",
		InspectionBookNumberAssignToUserID:    "",
		AssingedToUserID:                      "",
		AssingedToUserEmail:                   "<EMAIL>",
		Name:                                  randomize.RandomString(10),
		ModelNumber:                           randomize.RandomString(6),
		SerialNumber:                          randomize.RandomString(8),
		AssetCategoryCode:                     randomize.RandomString(6),
		SubCategoryCode:                       randomize.RandomString(6),
		ProductionDate:                        randomize.RandomDate(),
		Cost:                                  rand.Intn(100000),
		AssetStatusCode:                       randomize.RandomString(6),
		OwnershipCategoryCode:                 randomize.RandomString(8),
		RegistrationNumber:                    randomize.RandomString(8),
		ProductionYear:                        2023,
		NumberOfTyres:                         rand.Intn(10),
		EngineModel:                           randomize.RandomString(8),
		TransmissionModel:                     randomize.RandomString(8),
		VrdNumber:                             randomize.RandomString(8),
		VrdExpiryDate:                         randomize.RandomTime().Format(time.DateOnly),
		EngineNumber:                          randomize.RandomString(8),
		ChassisNumber:                         randomize.RandomString(8),
		GpsDeviceImei:                         randomize.RandomString(15),
		RegistrationCertificateNumber:         randomize.RandomString(8),
		InspectionBookNumber:                  randomize.RandomString(8),
		InspectionBookExpiryDate:              randomize.RandomTime().Format(time.DateOnly),
		VehicleKM:                             rand.Float64() * 100000,
		VehicleHm:                             rand.Float64() * 1000,
		NumberOfSpareTyres:                    rand.Intn(5),
		UseKilometer:                          null.BoolFrom(true),
		UseHourmeter:                          null.BoolFrom(true),
	}
}

func GenerateRandomDataBulkUploadAssetLinkedVehicleTyreReq() dtos.BulkUploadAssetLinkedVehicleTyreReq {
	return dtos.BulkUploadAssetLinkedVehicleTyreReq{}
}

func GenerateTyreRandom() dtos.BulkUploadAssetTyreReq {
	return dtos.BulkUploadAssetTyreReq{
		ReferenceID:      "",
		IsSuccess:        false,
		FailedReason:     "",
		IsValidToProcess: false,
		BrandNo:          "BRD-GEN-1",
		BrandID:          "",
		TyreNo:           "TYR-GEN-7",
		TyreID:           "",
		Name:             randomize.RandomString(8),
		SerialNumber:     randomize.RandomString(8),
		Cost:             rand.Intn(100000),
		DateCode:         "1223",
		DOTCode:          "1212",
		TotalKM:          0,
		StartThreadDepth: rand.Float64() * 20,
		TotalHm:          9,
	}
}
