BEGIN;

ALTER TABLE ams_asset_tyres
ADD COLUMN IF NOT EXISTS is_spare BOOLEAN DEFAULT NULL;

ALTER TABLE ams_linked_asset_vehicle_tyres
ADD COLUMN IF NOT EXISTS is_spare BOOLEAN DEFAULT false;

WITH
    cte AS (
        SELECT
            child_asset_id,
            number_of_tyres,
            alavt.tyre_position
        FROM
            ams_linked_assets ala
            JOIN ams_linked_asset_vehicle_tyres alavt ON alavt.asset_linked_id = ala.id
            JOIN ams_asset_vehicles aav ON aav.asset_id = ala.parent_asset_id
        WHERE
            linked_asset_type_code = 'VEHICLE_TYRE'
            AND unlinked_datetime IS NULL
            AND alavt.tyre_position > aav.number_of_tyres
            AND ala.deleted_at IS NULL
    )
UPDATE ams_asset_tyres aat
SET
    is_spare = TRUE
FROM
    cte
WHERE
    cte.child_asset_id = aat.asset_id;

WITH
    cte AS (
        SELECT
            child_asset_id,
            number_of_tyres,
            alavt.tyre_position
        FROM
            ams_linked_assets ala
            JOIN ams_linked_asset_vehicle_tyres alavt ON alavt.asset_linked_id = ala.id
            JOIN ams_asset_vehicles aav ON aav.asset_id = ala.parent_asset_id
        WHERE
            linked_asset_type_code = 'VEHICLE_TYRE'
            AND unlinked_datetime IS NULL
            AND alavt.tyre_position <= aav.number_of_tyres
            AND ala.deleted_at IS NULL
    )
UPDATE ams_asset_tyres aat
SET
    is_spare = FALSE
FROM
    cte
WHERE
    cte.child_asset_id = aat.asset_id;


WITH
    cte AS (
        SELECT
            child_asset_id,
            number_of_tyres,
            alavt.tyre_position,
            ala.id
        FROM
            ams_linked_assets ala
            JOIN ams_linked_asset_vehicle_tyres alavt ON alavt.asset_linked_id = ala.id
            JOIN ams_asset_vehicles aav ON aav.asset_id = ala.parent_asset_id
        WHERE
            linked_asset_type_code = 'VEHICLE_TYRE'
            AND unlinked_datetime IS NULL
            AND alavt.tyre_position > aav.number_of_tyres
    )
UPDATE ams_linked_asset_vehicle_tyres alavt
SET
    is_spare = TRUE
FROM
    cte
WHERE
    cte.id = alavt.asset_linked_id;


COMMIT;