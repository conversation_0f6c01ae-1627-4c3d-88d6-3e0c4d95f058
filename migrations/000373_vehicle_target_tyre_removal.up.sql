BEGIN;

CREATE TABLE IF NOT EXISTS "ams_vehicle_target_tyre_removals" (
    "id" character varying(40) NOT NULL PRIMARY KEY,
    target_rtd numeric(10, 2) NOT NULL,
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL
);

ALTER TABLE ams_asset_vehicles
ADD COLUMN IF NOT EXISTS target_removal_id VARCHAR(40) REFERENCES ams_vehicle_target_tyre_removals (id);

INSERT INTO ams_vehicle_target_tyre_removals (id, target_rtd, client_id, created_at, updated_at, created_by, updated_by)
SELECT 'vtr_' || SUBSTRING(id from 5), 0, id, NOW(), NOW(), 'ADMIN', 'ADMIN'
FROM uis_clients where deleted_at is null;

UPDATE ams_asset_vehicles
SET target_removal_id = 'vtr_' || SUBSTRING(client_id from 5)
WHERE target_removal_id IS NULL;

COMMIT;