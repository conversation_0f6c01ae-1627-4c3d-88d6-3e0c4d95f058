{"info": {"_postman_id": "a8e5f3b2-7c8d-4e5f-9a1b-c2d3e4f5a6b7", "name": "Vehicle Target Tyre Removals API", "description": "API endpoints for managing vehicle target tyre removals", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create Vehicle Target Tyre Removal", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"target_rtd\": 5.0,\n    \"asset_ids\": [\"asset_id_1\", \"asset_id_2\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/vehicle-target-tyre-removals", "host": ["{{base_url}}"], "path": ["v1", "vehicle-target-tyre-removals"]}, "description": "Creates a new vehicle target tyre removal with the specified target RTD and associated asset IDs."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"target_rtd\": 5.0,\n    \"asset_ids\": [\"asset_id_1\", \"asset_id_2\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/vehicle-target-tyre-removals", "host": ["{{base_url}}"], "path": ["v1", "vehicle-target-tyre-removals"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Vehicle target tyre removal created successfully\",\n    \"referenceId\": \"vtr_12345abcde\",\n    \"data\": {\n        \"id\": \"vtr_12345abcde\",\n        \"target_rtd\": 5.0,\n        \"asset_ids\": [\"asset_id_1\", \"asset_id_2\"],\n        \"created_at\": \"2023-07-01T12:00:00Z\"\n    }\n}"}]}, {"name": "Update Vehicle Target Tyre Removal", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"target_rtd\": 6.5,\n    \"asset_ids\": [\"asset_id_1\", \"asset_id_3\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/vehicle-target-tyre-removals/:id", "host": ["{{base_url}}"], "path": ["v1", "vehicle-target-tyre-removals", ":id"], "variable": [{"key": "id", "value": "vtr_12345abcde", "description": "ID of the vehicle target tyre removal to update"}]}, "description": "Updates an existing vehicle target tyre removal with the specified target RTD and associated asset IDs."}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"target_rtd\": 6.5,\n    \"asset_ids\": [\"asset_id_1\", \"asset_id_3\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/vehicle-target-tyre-removals/:id", "host": ["{{base_url}}"], "path": ["v1", "vehicle-target-tyre-removals", ":id"], "variable": [{"key": "id", "value": "vtr_12345abcde"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Vehicle target tyre removal updated successfully\",\n    \"referenceId\": \"vtr_12345abcde\",\n    \"data\": {\n        \"id\": \"vtr_12345abcde\",\n        \"target_rtd\": 6.5,\n        \"asset_ids\": [\"asset_id_1\", \"asset_id_3\"],\n        \"updated_at\": \"2023-07-01T13:00:00Z\"\n    }\n}"}]}, {"name": "Get Vehicle Target Tyre Removal List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/vehicle-target-tyre-removals?page_size=10&page_no=1&search_keyword=", "host": ["{{base_url}}"], "path": ["v1", "vehicle-target-tyre-removals"], "query": [{"key": "page_size", "value": "10", "description": "Number of items per page"}, {"key": "page_no", "value": "1", "description": "Page number"}, {"key": "search_keyword", "value": "", "description": "Optional search keyword"}]}, "description": "Retrieves a paginated list of vehicle target tyre removals."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/vehicle-target-tyre-removals?page_size=10&page_no=1&search_keyword=", "host": ["{{base_url}}"], "path": ["v1", "vehicle-target-tyre-removals"], "query": [{"key": "page_size", "value": "10"}, {"key": "page_no", "value": "1"}, {"key": "search_keyword", "value": ""}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"total_records\": 2,\n    \"page_size\": 10,\n    \"page_no\": 1,\n    \"data\": [\n        {\n            \"id\": \"vtr_12345abcde\",\n            \"target_rtd\": 6.5,\n            \"count_asset\": 2,\n            \"asset_reference_numbers\": [\"REF001\", \"REF003\"],\n            \"asset_serial_numbers\": [\"SN001\", \"SN003\"],\n            \"created_at\": \"2023-07-01T12:00:00Z\",\n            \"updated_at\": \"2023-07-01T13:00:00Z\"\n        },\n        {\n            \"id\": \"vtr_67890fghij\",\n            \"target_rtd\": 4.0,\n            \"count_asset\": 3,\n            \"asset_reference_numbers\": [\"REF004\", \"REF005\", \"REF006\"],\n            \"asset_serial_numbers\": [\"SN004\", \"SN005\", \"SN006\"],\n            \"created_at\": \"2023-07-02T10:00:00Z\",\n            \"updated_at\": \"2023-07-02T10:00:00Z\"\n        }\n    ]\n}"}]}, {"name": "Get Vehicle Target Tyre Removal by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/vehicle-target-tyre-removals/:id", "host": ["{{base_url}}"], "path": ["v1", "vehicle-target-tyre-removals", ":id"], "variable": [{"key": "id", "value": "vtr_12345abcde", "description": "ID of the vehicle target tyre removal to retrieve"}]}, "description": "Retrieves detailed information about a specific vehicle target tyre removal by its ID."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/vehicle-target-tyre-removals/:id", "host": ["{{base_url}}"], "path": ["v1", "vehicle-target-tyre-removals", ":id"], "variable": [{"key": "id", "value": "vtr_12345abcde"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Vehicle target tyre removal retrieved successfully\",\n    \"referenceId\": \"\",\n    \"data\": {\n        \"id\": \"vtr_12345abcde\",\n        \"target_rtd\": 6.5,\n        \"asset_ids\": [\"asset_id_1\", \"asset_id_3\"],\n        \"assets\": [\n            {\n                \"id\": \"asset_id_1\",\n                \"name\": \"Vehicle 1\",\n                \"serial_number\": \"SN001\",\n                \"reference_number\": \"REF001\",\n                \"brand_name\": \"Brand A\",\n                \"status_code\": \"ACTIVE\"\n            },\n            {\n                \"id\": \"asset_id_3\",\n                \"name\": \"Vehicle 3\",\n                \"serial_number\": \"SN003\",\n                \"reference_number\": \"REF003\",\n                \"brand_name\": \"Brand B\",\n                \"status_code\": \"ACTIVE\"\n            }\n        ],\n        \"created_at\": \"2023-07-01T12:00:00Z\",\n        \"updated_at\": \"2023-07-01T13:00:00Z\",\n        \"created_by\": \"user_123\",\n        \"updated_by\": \"user_123\"\n    }\n}"}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "token", "value": "your_auth_token_here", "type": "string"}]}