package persistence

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
)

func enrichAssetTyreReplacementForecastQueryWithWhere(query *gorm.DB, where models.AssetTyreReplacementForecastWhere) {
	if where.ClientID != "" {
		query.Where("ams_asset_tyres.client_id = ?", where.ClientID)
	} // ClientID
}

// GetAssetTyreReplacementForecastReport
func (ar *AssetTyreRepository) GetAssetTyreReplacementForecastReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreReplacementForecastReportParam) (int, []models.AssetTyre, error) {
	var totalRecords int64
	assets := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{})
	query.Where("ams_assets.asset_status_code = ?", constants.ASSET_STATUS_CODE_INSTALLED)

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		// join to linked assets
		Joins("JOIN ams_linked_assets ON ams_linked_assets.child_asset_id = ams_asset_tyres.asset_id AND linked_asset_type_code = 'VEHICLE_TYRE' AND ams_linked_assets.unlinked_datetime IS NULL").
		// join to linked asset vehicle tyres
		Joins("JOIN ams_linked_asset_vehicle_tyres ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		// join to asset vehicle
		Joins("JOIN ams_assets AS ams_assets_vehicle ON ams_assets_vehicle.id = ams_linked_assets.parent_asset_id")

	query.Where("utilization_rate_percentage_status_code IN ?", []string{constants.UTILIZATION_RATE_PERCENTAGE_STATUS_CODE_CRITICAL, constants.UTILIZATION_RATE_PERCENTAGE_STATUS_CODE_LOW})

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	enrichAssetTyreReplacementForecastQueryWithWhere(query, param.Cond.Where)

	query.Preload("Tyre").
		Preload("Asset").
		Preload("RetreadTyre", func(db *gorm.DB) *gorm.DB {
			return db.Order("thread_sequence asc")
		}).
		Preload("Asset.Brand").
		Preload("AssetLinked", func(db *gorm.DB) *gorm.DB {
			return db.Where("linked_asset_type_code = ?", constants.ASSET_LINKED_TYPE_VEHICLE_TYRE).Where("unlinked_datetime IS NULL")
		}).
		Preload("AssetLinked.AssetParent").
		Preload("AssetLinked.AssetLinkedAssetVehicleTyre").
		Preload("AssetLinked.AssetParent.CustomAssetSubCategory")

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assets, nil
	}

	query.Order("ams_asset_tyres.utilization_rate_percentage DESC")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assets, nil
}

// GetAssetTyreReplacementForecastReportExport
func (ar *AssetTyreRepository) GetAssetTyreReplacementForecastReportExport(ctx context.Context, dB database.DBI, param models.GetAssetTyreReplacementForecastReportParam) ([]models.AssetTyre, error) {
	assets := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{})
	query.Where("ams_assets.asset_status_code = ?", constants.ASSET_STATUS_CODE_INSTALLED)

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		// join to linked assets
		Joins("JOIN ams_linked_assets ON ams_linked_assets.child_asset_id = ams_asset_tyres.asset_id AND linked_asset_type_code = 'VEHICLE_TYRE' AND ams_linked_assets.unlinked_datetime IS NULL").
		// join to linked asset vehicle tyres
		Joins("JOIN ams_linked_asset_vehicle_tyres ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		// join to asset vehicle
		Joins("JOIN ams_assets AS ams_assets_vehicle ON ams_assets_vehicle.id = ams_linked_assets.parent_asset_id")

	query.Where("utilization_rate_percentage_status_code IN ?", []string{constants.UTILIZATION_RATE_PERCENTAGE_STATUS_CODE_CRITICAL, constants.UTILIZATION_RATE_PERCENTAGE_STATUS_CODE_LOW})

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	enrichAssetTyreReplacementForecastQueryWithWhere(query, param.Cond.Where)

	query.Preload("Tyre").
		Preload("Asset").
		Preload("RetreadTyre", func(db *gorm.DB) *gorm.DB {
			return db.Order("thread_sequence asc")
		}).
		Preload("Asset.Brand").
		Preload("AssetLinked", func(db *gorm.DB) *gorm.DB {
			return db.Where("linked_asset_type_code = ?", constants.ASSET_LINKED_TYPE_VEHICLE_TYRE).Where("unlinked_datetime IS NULL")
		}).
		Preload("AssetLinked.AssetParent").
		Preload("AssetLinked.AssetLinkedAssetVehicleTyre").
		Preload("AssetLinked.AssetParent.CustomAssetSubCategory")

	query.Order("ams_asset_tyres.utilization_rate_percentage DESC")

	err := query.Find(&assets).Error
	if err != nil {
		return nil, err
	}

	return assets, nil
}

func enrichAssetTyreUsageQueryWithWhere(query *gorm.DB, where models.AssetTyreUsageWhere) {
	if !where.StartDate.IsZero() {
		query.Where("ams_asset_tyres.first_installed_datetime > ?", where.StartDate)
	}

	if !where.EndDate.IsZero() {
		query.Where("ams_asset_tyres.first_installed_datetime < ?", where.EndDate)
	}

	if where.ClientID != "" {
		query.Where("ams_asset_tyres.client_id = ?", where.ClientID)
	} // ClientID

	if len(where.BrandIDs) > 0 {
		query.Where("ams_assets.brand_id IN ?", where.BrandIDs)
	}

	if len(where.PatternTypes) > 0 {
		query.Where("ams_tyres.pattern_type IN ?", where.PatternTypes)
	}

	if len(where.TyreSizes) > 0 {
		query.Where("CONCAT(ams_tyres.section_width, ' ', ams_tyres.construction, ' ', ams_tyres.rim_diameter) IN ?", where.TyreSizes)
	}

	if len(where.ParentCustomAssetSubcategoryIDs) > 0 {
		query.Where("ams_assets_vehicle.custom_asset_sub_category_id IN ?", where.ParentCustomAssetSubcategoryIDs)
	}

}

// GetAssetTyreReplacementForecaseReport
func (ar *AssetTyreRepository) GetAssetTyreUsageReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreUsageReportParam) (int, []models.AssetTyre, error) {
	var totalRecords int64
	assets := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{})

	query.Where("ams_asset_tyres.first_installed_datetime IS NOT NULL")

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		Joins("LEFT JOIN ams_linked_assets ON ams_asset_tyres.asset_id = ams_linked_assets.child_asset_id AND linked_asset_type_code = 'VEHICLE_TYRE' AND ams_linked_assets.unlinked_datetime IS NULL").
		Joins("LEFT JOIN ams_assets AS ams_assets_vehicle ON ams_assets_vehicle.id = ams_linked_assets.parent_asset_id")

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	enrichAssetTyreUsageQueryWithWhere(query, param.Cond.Where)

	query.Preload("Tyre").
		Preload("Asset").
		Preload("RetreadTyre", func(db *gorm.DB) *gorm.DB {
			return db.Order("thread_sequence asc")
		}).
		Preload("Asset.Brand").
		Preload("AssetLinked", func(db *gorm.DB) *gorm.DB {
			return db.Where("linked_asset_type_code = ?", constants.ASSET_LINKED_TYPE_VEHICLE_TYRE).Where("unlinked_datetime IS NULL")
		}).
		Preload("AssetLinked.AssetParent").
		Preload("AssetLinked.AssetLinkedAssetVehicleTyre").
		Preload("AssetLinked.AssetParent.CustomAssetSubCategory")

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assets, nil
	}

	query.Order("ams_asset_tyres.first_installed_datetime DESC")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assets, nil
}

// GetAssetTyreReplacementForecaseReportExport
func (ar *AssetTyreRepository) GetAssetTyreUsageReportExport(ctx context.Context, dB database.DBI, param models.GetAssetTyreUsageReportParam) ([]models.AssetTyre, error) {
	assets := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{})

	query.Where("ams_asset_tyres.first_installed_datetime IS NOT NULL")

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		Joins("LEFT JOIN ams_linked_assets ON ams_asset_tyres.asset_id = ams_linked_assets.child_asset_id AND linked_asset_type_code = 'VEHICLE_TYRE' AND ams_linked_assets.unlinked_datetime IS NULL").
		Joins("LEFT JOIN ams_assets AS ams_assets_vehicle ON ams_assets_vehicle.id = ams_linked_assets.parent_asset_id")

	enrichAssetTyreUsageQueryWithWhere(query, param.Cond.Where)

	query.Preload("Tyre").
		Preload("Asset").
		Preload("RetreadTyre", func(db *gorm.DB) *gorm.DB {
			return db.Order("thread_sequence asc")
		}).
		Preload("Asset.Brand").
		Preload("AssetLinked", func(db *gorm.DB) *gorm.DB {
			return db.Where("unlinked_datetime IS NULL")
		}).
		Preload("AssetLinked.AssetParent").
		Preload("AssetLinked.AssetLinkedAssetVehicleTyre").
		Preload("AssetLinked.AssetParent.CustomAssetSubCategory")

	query.Order("ams_asset_tyres.first_installed_datetime DESC")

	err := query.Find(&assets).Error
	if err != nil {
		return nil, err
	}

	return assets, nil
}

func (ar *AssetTyreRepository) GetAssetTyreInspectionReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreInspectionReportParam) (int, []models.AssetTyre, error) {
	var totalRecords int64
	assetTyres := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{}).
		Where("ams_asset_tyres.last_inspected_at IS NOT NULL")

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		// join to linked assets
		Joins("JOIN ams_linked_assets ON ams_linked_assets.child_asset_id = ams_asset_tyres.asset_id AND linked_asset_type_code = 'VEHICLE_TYRE' AND ams_linked_assets.unlinked_datetime IS NULL").
		// join to linked asset vehicle tyres
		Joins("JOIN ams_linked_asset_vehicle_tyres ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		// join to asset vehicle
		Joins("JOIN ams_assets AS ams_assets_vehicle ON ams_assets_vehicle.id = ams_linked_assets.parent_asset_id")

	enrichAssetTyreInspectionQueryWithWhere(query, param.Cond.Where)

	query.Preload("Tyre").
		Preload("Asset").
		Preload("RetreadTyre", func(db *gorm.DB) *gorm.DB {
			return db.Order("thread_sequence asc")
		}).
		Preload("Asset.Brand").
		Preload("AssetLinked", func(db *gorm.DB) *gorm.DB {
			return db.Where("linked_asset_type_code = ?", constants.ASSET_LINKED_TYPE_VEHICLE_TYRE).
				Where("unlinked_datetime IS NULL")
		}).
		Preload("AssetLinked.AssetParent").
		Preload("AssetLinked.AssetLinkedAssetVehicleTyre").
		Preload("AssetLinked.AssetParent.CustomAssetSubCategory")

	if param.SearchKeyword != "" {
		query. // where search condition
			Where(
				query.Session(&gorm.Session{NewDB: true}).
					Unscoped().
					Where("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
			)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assetTyres, nil
	}

	query.Order("ams_asset_tyres.last_inspected_at DESC")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assetTyres).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetTyres, nil
}

func enrichAssetTyreInspectionQueryWithWhere(query *gorm.DB, where models.AssetTyreInspectionReportWhere) {
	if where.ClientID != "" {
		query.Where("ams_asset_tyres.client_id = ?", where.ClientID)
	} // ClientID

	if !where.StartDate.IsZero() {
		query.Where("ams_asset_tyres.last_inspected_at >= ?", where.StartDate)
	}

	if !where.EndDate.IsZero() {
		query.Where("ams_asset_tyres.last_inspected_at < ?", where.EndDate)
	}

	if len(where.BrandIDs) > 0 {
		query.Where("ams_assets.brand_id IN ?", where.BrandIDs)
	}

	if len(where.TyreSizes) > 0 {
		query.Where("CONCAT(ams_tyres.section_width, ' ', ams_tyres.construction, ' ', ams_tyres.rim_diameter) IN ?", where.TyreSizes)
	}

	if len(where.PatternTypes) > 0 {
		query.Where("ams_tyres.pattern_type IN ?", where.PatternTypes)
	}

	if len(where.StatusCodes) > 0 {
		query.Where("ams_assets.asset_status_code IN ?", where.StatusCodes)
	}

	if len(where.UtilizationRateStatusCodes) > 0 {
		query.Where("ams_asset_tyres.utilization_rate_percentage_status_code IN ?", where.UtilizationRateStatusCodes)
	}
}

func (r *AssetTyreRepository) GetAssetTyreInspectionReportExport(ctx context.Context, dB database.DBI, param models.GetAssetTyreInspectionReportParam) ([]models.AssetTyre, error) {
	assetTyres := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{}).
		Where("ams_asset_tyres.last_inspected_at IS NOT NULL")

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		// join to linked assets
		Joins("JOIN ams_linked_assets ON ams_linked_assets.child_asset_id = ams_asset_tyres.asset_id AND linked_asset_type_code = 'VEHICLE_TYRE' AND ams_linked_assets.unlinked_datetime IS NULL").
		// join to linked asset vehicle tyres
		Joins("JOIN ams_linked_asset_vehicle_tyres ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		// join to asset vehicle
		Joins("JOIN ams_assets AS ams_assets_vehicle ON ams_assets_vehicle.id = ams_linked_assets.parent_asset_id")

	enrichAssetTyreInspectionQueryWithWhere(query, param.Cond.Where)

	query.Preload("Tyre").
		Preload("Asset").
		Preload("RetreadTyre", func(db *gorm.DB) *gorm.DB {
			return db.Order("thread_sequence asc")
		}).
		Preload("Asset.Brand").
		Preload("AssetLinked", func(db *gorm.DB) *gorm.DB {
			return db.Where("linked_asset_type_code = ?", constants.ASSET_LINKED_TYPE_VEHICLE_TYRE).
				Where("unlinked_datetime IS NULL")
		}).
		Preload("AssetLinked.AssetParent").
		Preload("AssetLinked.AssetLinkedAssetVehicleTyre").
		Preload("AssetLinked.AssetParent.CustomAssetSubCategory")

	query.Order("ams_asset_tyres.last_inspected_at DESC")

	err := query.Find(&assetTyres).Error
	if err != nil {
		return nil, err
	}

	return assetTyres, nil
}

func enrichAssetTyreRotationQueryWithWhere(query *gorm.DB, where models.AssetTyreRotationReportWhere) {
	if where.ClientID != "" {
		query.Where("ams_linked_assets.client_id = ?", where.ClientID)
	} // ClientID

	if !where.StartDate.IsZero() {
		query.Where("ams_linked_assets.unlinked_datetime >= ?", where.StartDate)
	}

	if !where.EndDate.IsZero() {
		query.Where("ams_linked_assets.unlinked_datetime < ?", where.EndDate)
	}

}

func (r *AssetTyreRepository) GetAssetTyreRotationReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreRotationReportParam) (int, []models.TyreRotationReportModel, error) {
	var totalRecords int64
	tyreRotations := []models.TyreRotationReportModel{}
	query := dB.GetTx().Model(&models.TyreRotationReportModel{})

	query.Joins(`
JOIN ams_linked_assets ala2 ON
	ala2.parent_asset_id = ams_linked_assets.parent_asset_id
	AND ala2.child_asset_id = ams_linked_assets.child_asset_id
	AND ams_linked_assets.linked_asset_type_code = 'VEHICLE_TYRE'
	AND ala2.linked_asset_type_code = 'VEHICLE_TYRE'
	AND ams_linked_assets.unlinked_datetime = ala2.linked_datetime
JOIN ams_linked_asset_vehicle_tyres alavt ON
	ams_linked_assets.id = alavt.asset_linked_id
JOIN ams_linked_asset_vehicle_tyres alavt2 ON
	alavt2.asset_linked_id = ala2.id`)

	query.
		// join to assets via linked assets
		Joins("INNER JOIN ams_assets ON ams_assets.id = ams_linked_assets.child_asset_id AND ams_assets.deleted_at IS NULL")

	enrichAssetTyreRotationQueryWithWhere(query, param.Cond.Where)

	if param.SearchKeyword != "" {
		query. // where search condition
			Where(
				query.Session(&gorm.Session{NewDB: true}).
					Unscoped().
					Where("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
			)
	}

	query.Select(
		"alavt.tyre_position AS prev_tyre_position",
		"alavt2.tyre_position AS new_tyre_position",
		"ams_linked_assets.unlinked_datetime",
		"ams_linked_assets.child_asset_id",
		"ams_linked_assets.parent_asset_id",
	)

	query.
		Preload("ChildAsset").
		Preload("ChildAsset.Tyre").
		Preload("ChildAsset.Asset").
		Preload("ChildAsset.Asset.Brand").
		Preload("AssetParent").
		Preload("AssetParent.CustomAssetSubCategory")

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), tyreRotations, nil
	}

	query.Order("ams_linked_assets.unlinked_datetime DESC")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&tyreRotations).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), tyreRotations, nil
}

func (r *AssetTyreRepository) GetAssetTyreRotationReportExport(ctx context.Context, dB database.DBI, param models.GetAssetTyreRotationReportParam) ([]models.TyreRotationReportModel, error) {
	tyreRotations := []models.TyreRotationReportModel{}
	query := dB.GetTx().Model(&models.TyreRotationReportModel{})

	query.Joins(`
JOIN ams_linked_assets ala2 ON
	ala2.parent_asset_id = ams_linked_assets.parent_asset_id
	AND ala2.child_asset_id = ams_linked_assets.child_asset_id
	AND ams_linked_assets.linked_asset_type_code = 'VEHICLE_TYRE'
	AND ala2.linked_asset_type_code = 'VEHICLE_TYRE'
	AND ams_linked_assets.unlinked_datetime = ala2.linked_datetime
JOIN ams_linked_asset_vehicle_tyres alavt ON
	ams_linked_assets.id = alavt.asset_linked_id
JOIN ams_linked_asset_vehicle_tyres alavt2 ON
	alavt2.asset_linked_id = ala2.id`)

	query.
		// join to assets via linked assets
		Joins("INNER JOIN ams_assets ON ams_assets.id = ams_linked_assets.child_asset_id AND ams_assets.deleted_at IS NULL")

	enrichAssetTyreRotationQueryWithWhere(query, param.Cond.Where)

	query.Select(
		"alavt.tyre_position AS prev_tyre_position",
		"alavt2.tyre_position AS new_tyre_position",
		"ams_linked_assets.unlinked_datetime",
		"ams_linked_assets.child_asset_id",
		"ams_linked_assets.parent_asset_id",
	)

	query.
		Preload("ChildAsset").
		Preload("ChildAsset.Tyre").
		Preload("ChildAsset.Asset").
		Preload("ChildAsset.Asset.Brand").
		Preload("AssetParent").
		Preload("AssetParent.CustomAssetSubCategory")

	err := query.Find(&tyreRotations).Error
	if err != nil {
		return nil, err
	}

	return tyreRotations, nil
}
