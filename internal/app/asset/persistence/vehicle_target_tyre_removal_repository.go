package persistence

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
)

type VehicleTargetTyreRemovalRepository struct{}

func NewVehicleTargetTyreRemovalRepository() repository.VehicleTargetTyreRemovalRepository {
	return &VehicleTargetTyreRemovalRepository{}
}

func (r *VehicleTargetTyreRemovalRepository) CreateVehicleTargetTyreRemoval(ctx context.Context, dB database.DBI, removal *models.VehicleTargetTyreRemoval) error {
	return dB.GetTx().Create(removal).Error
}

func (r *VehicleTargetTyreRemovalRepository) GetVehicleTargetTyreRemovalByID(ctx context.Context, dB database.DBI, id string) (*models.VehicleTargetTyreRemoval, error) {
	var removal models.VehicleTargetTyreRemoval
	err := dB.GetTx().
		Where("id = ?", id).
		Preload("AssetVehicles").
		Preload("AssetVehicles.Asset").
		Preload("AssetVehicles.Asset.Brand").
		First(&removal).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &removal, nil
}

func (r *VehicleTargetTyreRemovalRepository) GetVehicleTargetTyreRemoval(ctx context.Context, dB database.DBI, condition models.VehicleTargetTyreRemovalCondition) (*models.VehicleTargetTyreRemoval, error) {
	var removal models.VehicleTargetTyreRemoval
	query := dB.GetTx().Model(&models.VehicleTargetTyreRemoval{})

	enrichVehicleTargetTyreRemovalQueryWithWhere(query, condition.Where)
	enrichVehicleTargetTyreRemovalQueryWithPreload(query, condition.Preload)

	err := query.First(&removal).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("vehicle target tyre removal")
		}
		return nil, err
	}
	return &removal, nil
}

func (r *VehicleTargetTyreRemovalRepository) GetVehicleTargetTyreRemovals(ctx context.Context, dB database.DBI, condition models.VehicleTargetTyreRemovalCondition) ([]models.VehicleTargetTyreRemoval, error) {
	var removals []models.VehicleTargetTyreRemoval
	query := dB.GetTx().Model(&models.VehicleTargetTyreRemoval{})

	enrichVehicleTargetTyreRemovalQueryWithWhere(query, condition.Where)
	enrichVehicleTargetTyreRemovalQueryWithPreload(query, condition.Preload)

	err := query.Find(&removals).Error
	if err != nil {
		return nil, err
	}
	return removals, nil
}

func (r *VehicleTargetTyreRemovalRepository) GetVehicleTargetTyreRemovalList(ctx context.Context, dB database.DBI, param models.GetVehicleTargetTyreRemovalListParam) (int, []models.VehicleTargetTyreRemoval, error) {
	var totalRecords int64
	var removals []models.VehicleTargetTyreRemoval

	query := dB.GetTx().Model(&models.VehicleTargetTyreRemoval{})

	enrichVehicleTargetTyreRemovalQueryWithWhere(query, param.Cond.Where)

	// Apply search filter if provided
	if param.SearchKeyword != "" {
		query.Where("CAST(target_rtd AS TEXT) LIKE ?", "%"+param.SearchKeyword+"%")
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), removals, nil
	}

	query.Order("updated_at DESC")

	enrichVehicleTargetTyreRemovalQueryWithPreload(query, param.Cond.Preload)

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&removals).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), removals, nil
}

func (r *VehicleTargetTyreRemovalRepository) UpdateVehicleTargetTyreRemoval(ctx context.Context, dB database.DBI, id string, removal *models.VehicleTargetTyreRemoval) error {
	return dB.GetTx().
		Model(&models.VehicleTargetTyreRemoval{}).
		Where("id = ?", id).
		Updates(removal).Error
}

func (r *VehicleTargetTyreRemovalRepository) DeleteVehicleTargetTyreRemoval(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().Delete(&models.VehicleTargetTyreRemoval{}, "id = ?", id).Error
}

func (r *VehicleTargetTyreRemovalRepository) UpdateAssetVehicleTargetRemovalIDs(ctx context.Context, dB database.DBI, targetRemovalID string, assetIDs []string) error {
	if len(assetIDs) > 0 {
		err := dB.GetTx().Model(&models.AssetVehicle{}).
			Where("asset_id IN ?", assetIDs).
			Update("target_removal_id", targetRemovalID).Error
		if err != nil {
			return err
		}
	}

	return nil
}

func (r *VehicleTargetTyreRemovalRepository) CheckTargetRTDAvailability(ctx context.Context, dB database.DBI, targetRTD float64, clientID string) (bool, error) {
	var count int64
	err := dB.GetTx().Model(&models.VehicleTargetTyreRemoval{}).
		Where("target_rtd = ? AND client_id = ?", targetRTD, clientID).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	// Return true if available (count is 0), false if already exists
	return count == 0, nil
}

// Helper functions for query building
func enrichVehicleTargetTyreRemovalQueryWithWhere(query *gorm.DB, where models.VehicleTargetTyreRemovalWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	}

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	}

	if where.TargetRTD.Valid {
		query.Where("target_rtd = ?", where.TargetRTD.Float64)
	}

	if len(where.AssetIDs) > 0 {
		query.Joins("JOIN ams_asset_vehicles ON ams_vehicle_target_tyre_removals.id = ams_asset_vehicles.target_removal_id").
			Where("ams_asset_vehicles.asset_id IN ?", where.AssetIDs)
	}
}

func enrichVehicleTargetTyreRemovalQueryWithPreload(query *gorm.DB, preload models.VehicleTargetTyreRemovalPreload) {
	if preload.AssetVehicles {
		query.Preload("AssetVehicles.Asset")
	}
}
