package persistence

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
)

type locationRepository struct{}

func NewLocationRepository() repository.LocationRepository {
	return &locationRepository{}
}

func (r *locationRepository) CreateLocation(ctx context.Context, dB database.DBI, loc *models.Location) error {
	return dB.GetTx().Create(loc).Error
}

func enrichLocationQueryWithWhere(query *gorm.DB, where models.LocationWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if !where.ShowDeleted {
		query.Where("status_code != ?", constants.LOCATION_STATUS_CODE_DELETED)
	} // ShowDeleted

	if where.WithOrmDeleted {
		query.Unscoped()
	}

	if len(where.StatusCodes) > 0 {
		query.Where("status_code in ?", where.StatusCodes)
	} // StatusCodes
}

func (r *locationRepository) GetLocation(ctx context.Context, dB database.DBI, cond models.LocationCondition) (*models.Location, error) {
	location := models.Location{}
	query := dB.GetOrm().Model(&location)

	enrichLocationQueryWithWhere(query, cond.Where)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&location).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("location")
		}

		return nil, err
	}

	return &location, nil
}

func (r *locationRepository) GetLocationList(ctx context.Context, dB database.DBI, param models.GetLocationListParam) (int, []models.Location, error) {
	var totalRecords int64
	locs := []models.Location{}
	query := dB.GetTx().Model(&locs)
	enrichLocationQueryWithWhere(query, param.Cond.Where)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().Where("LOWER(name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(address) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&locs).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), locs, nil

}

func (r *locationRepository) UpdateLocation(ctx context.Context, dB database.DBI, id string, loc *models.Location) error {
	return dB.GetTx().
		Model(&models.Location{}).
		Where("id = ?", id).
		Updates(loc).
		Error
}

func (r *locationRepository) GetLocationByIds(ctx context.Context, dB database.DBI, locations *[]models.Location, locationIds []string) error {
	if err := dB.GetOrm().Where("ID IN ?", locationIds).Find(locations).Error; err != nil {
		return err
	}
	return nil
}
