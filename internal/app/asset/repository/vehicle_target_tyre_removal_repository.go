package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type VehicleTargetTyreRemovalRepository interface {
	CreateVehicleTargetTyreRemoval(ctx context.Context, dB database.DBI, removal *models.VehicleTargetTyreRemoval) error
	GetVehicleTargetTyreRemoval(ctx context.Context, dB database.DBI, condition models.VehicleTargetTyreRemovalCondition) (*models.VehicleTargetTyreRemoval, error)
	GetVehicleTargetTyreRemovals(ctx context.Context, dB database.DBI, condition models.VehicleTargetTyreRemovalCondition) ([]models.VehicleTargetTyreRemoval, error)
	GetVehicleTargetTyreRemovalList(ctx context.Context, dB database.DBI, param models.GetVehicleTargetTyreRemovalListParam) (int, []models.VehicleTargetTyreRemoval, error)
	UpdateVehicleTargetTyreRemoval(ctx context.Context, dB database.DBI, id string, removal *models.VehicleTargetTyreRemoval) error
	DeleteVehicleTargetTyreRemoval(ctx context.Context, dB database.DBI, id string) error
	UpdateAssetVehicleTargetRemovalIDs(ctx context.Context, dB database.DBI, targetRemovalID string, assetIDs []string) error
	CheckTargetRTDAvailability(ctx context.Context, dB database.DBI, targetRTD float64, clientID string) (bool, error)
}
