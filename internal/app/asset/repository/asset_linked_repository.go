package repository

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"context"
	"time"
)

type AssetLinkedRepository interface {
	GetAssetLinkeds(ctx context.Context, dB database.DBI, condition models.AssetLinkedCondition) ([]models.AssetLinked, error)
	GetAssetLinked(ctx context.Context, dB database.DBI, condition models.AssetLinkedCondition) (*models.AssetLinked, error)
	GetAssetLinkedTyres(ctx context.Context, dB database.DBI, assets *[]models.AssetLinkedAssetVehicleTyre, parentAssetId string) error
	GetAssetLinkedTyresV2(ctx context.Context, dB database.DBI, condition models.AssetLinkedVehicleTyreCondition) ([]models.AssetLinkedAssetVehicleTyre, error)
	GetAssetLinkedTyre(ctx context.Context, dB database.DBI, condition models.AssetLinkedVehicleTyreCondition) (*models.AssetLinkedAssetVehicleTyre, error)
	GetAssetTyrePosition(ctx context.Context, dB database.DBI, ids []string) ([]dtos.AssetTyrePosition, error)
	GetAssetTyrePositionById(ctx context.Context, dB database.DBI, id string) (int, error)
	GetAssetLinkedTyreHistory(ctx context.Context, dB database.DBI, req models.GetAssetLinkedListParam) (int, []models.AssetLinkedAssetVehicleTyre, error)
	GetAssetLinkedVehicleHistory(ctx context.Context, dB database.DBI, req models.GetAssetLinkedListParam) (int, []models.AssetLinkedAssetVehicleTyre, error)

	GetAssetLinkedByID(ctx context.Context, dB database.DBI, id string) (*models.AssetLinked, error)
	GetAssetLinkedAssetVehiceTyreByID(ctx context.Context, dB database.DBI, id string) (*models.AssetLinkedAssetVehicleTyre, error)

	GetAssetLinkedByChildAssetIds(ctx context.Context, dB database.DBI, assetLinkeds *[]models.AssetLinked, ids []string) error

	CreateAssetLinked(ctx context.Context, dB database.DBI, assetLinked *models.AssetLinked) error
	CreateAssetLinkeds(ctx context.Context, dB database.DBI, assetLinkeds []models.AssetLinked) error
	CreateAssetLinkedAssetVehiceTyre(ctx context.Context, dB database.DBI, AssetLinkedAssetVehicleTyre *models.AssetLinkedAssetVehicleTyre) error

	UpdateAssetLinked(ctx context.Context, dB database.DBI, assetLinked *models.AssetLinked) error
	UpdateAssetLinkedV2(ctx context.Context, dB database.DBI, id string, assetLinked *models.AssetLinked) error
	UnlinkAssetLinkedByIDs(ctx context.Context, dB database.DBI, IDs []string, timeNow time.Time) error
	UpdateAssetLinkedAssetVehicleTyre(ctx context.Context, dB database.DBI, AssetLinkedAssetVehicleTyre *models.AssetLinkedAssetVehicleTyre) error
	UpdateAssetLinkedAssetVehicleTyreV2(ctx context.Context, dB database.DBI, AssetLinkedAssetVehicleTyre *models.AssetLinkedAssetVehicleTyre) error

	UpdateClaimBonusPenaltyByIDs(ctx context.Context, dB database.DBI, IDs []string, cond bool) error
	GetAssetLinkedBonusPenaltyElig(ctx context.Context, dB database.DBI, req models.GetAssetLinkedListParam) (int, []dtos.AssetLinkedBonusPenaltyElig, error)
	GetAssetLinkedList(ctx context.Context, dB database.DBI, param models.GetAssetLinkedGeneralListParam) (int, []models.AssetLinked, error)
}
