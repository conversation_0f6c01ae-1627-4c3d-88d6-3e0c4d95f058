package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"
	"time"
)

type AssetTyreRepository interface {
	// GetAssetTyreList(ctx context.Context, dB database.DBI, param models.GetAssetTyreListParam) (int, []models.AssetTyre, error)
	GetAssetTyreListV2(ctx context.Context, dB database.DBI, param models.GetAssetTyreListParam) (int, []models.AssetTyre, error)
	GetAssetTyre(ctx context.Context, dB database.DBI, condition models.AssetTyreCondition) (*models.AssetTyre, error)
	GetAssetTyres(ctx context.Context, dB database.DBI, condition models.AssetTyreCondition) ([]models.AssetTyre, error)

	GetAssetTyresByIds(ctx context.Context, dB database.DBI, assets *[]models.AssetTyre, ids []string, param models.GetAssetTyreListParam) error
	GetAssetTyresByLinkedParentAssetId(ctx context.Context, dB database.DBI, assets *[]models.AssetTyre, parentAssetId string) error

	GetAssetTyreByID(ctx context.Context, dB database.DBI, id string) (*models.AssetTyre, error)
	GetAssetTyreBySerialNumber(ctx context.Context, dB database.DBI, serialNumber string) (*models.AssetTyre, error)

	CreateAssetTyre(ctx context.Context, dB database.DBI, assetTyre *models.AssetTyre) error
	GetTyres(ctx context.Context, dB database.DBI, tyres *[]models.Tyre, brandId string) error

	UpdateAssetTyre(ctx context.Context, dB database.DBI, assetTyre *models.AssetTyre) error
	UpdateAssetTyreByIDs(ctx context.Context, dB database.DBI, assetIDs []string, assetTyre *models.AssetTyre) error
	IncreaseAssetTyreKM(ctx context.Context, dB database.DBI, assetIDs []string, increaseKM int) error
	IncreaseAssetTyreHm(ctx context.Context, dB database.DBI, assetIDs []string, increaseHm int) error
	UpdateAssetTyreStatsHistory(ctx context.Context, dB database.DBI, assetID string) error
	RetreadAssetTyre(ctx context.Context, dB database.DBI, assetId string, newSTD float64) error
	UpdateAssetTyreAverageRTD(ctx context.Context, dB database.DBI, assetTyreId string, averageRTD float64) error

	GetTyreList(ctx context.Context, dB database.DBI, param models.GetTyreListParam) (int, []models.Tyre, error)
	GetTyreSizeList(ctx context.Context, dB database.DBI, param models.GetTyreSizeListParam) (int, []string, error)
	GetTyrePatternTypes(ctx context.Context, dB database.DBI, param models.GetTyrePatternTypeListParam) (int, []string, error)
	CreateTyre(ctx context.Context, dB database.DBI, Tyre *models.Tyre) error
	GetTyre(ctx context.Context, dB database.DBI, condition models.TyreCondition) (*models.Tyre, error)
	GetTyresV2(ctx context.Context, dB database.DBI, condition models.TyreCondition) ([]models.Tyre, error)
	DeleteTyreByID(ctx context.Context, dB database.DBI, id string) error
	UpdateTyre(ctx context.Context, dB database.DBI, tyre *models.Tyre) error

	CreateAssetTyreTread(ctx context.Context, dB database.DBI, tread *models.AssetTyreTread) error
	UpdateAssetTyreTread(ctx context.Context, dB database.DBI, id string, assetTyreTread *models.AssetTyreTread) error
	GetAssetTyreTread(ctx context.Context, dB database.DBI, cond models.AssetTyreTreadCondition) (*models.AssetTyreTread, error)
	GetAssetTyreTreadList(ctx context.Context, dB database.DBI, param models.GetAssetTyreTreadListParam) (int, []models.AssetTyreTread, error)
	GetTyresCSV(ctx context.Context, dB database.DBI, cond models.TyreCondition) ([]models.Tyre, error)

	PopulatePeriodicAssetTyreStatsHistory(ctx context.Context, dB database.DBI) error
	GetAssetTyreStatsHistory(ctx context.Context, dB database.DBI, datetime time.Time, assetID, clientID string) (*models.AssetTyreStatsHistory, error)
	IncreaseAssetTyreRepairedNumber(ctx context.Context, dB database.DBI, assetId string) error

	GetAssetTyreScrappedList(ctx context.Context, dB database.DBI, param models.GetAssetTyreScrappedDisposedListParam) (int, []models.AssetTyre, error)
	GetAssetTyreDisposedList(ctx context.Context, dB database.DBI, param models.GetAssetTyreScrappedDisposedListParam) (int, []models.AssetTyre, error)

	GetAssetTyresTreadConfig(ctx context.Context, dB database.DBI, cond models.AssetTyresTreadConfigCondition) (*models.AssetTyresTreadConfig, error)
	GetAssetTyresTreadConfigList(ctx context.Context, dB database.DBI, param models.GetAssetTyresTreadConfigListParam) (int, []models.AssetTyresTreadConfig, error)
	CreateAssetTyresTreadConfig(ctx context.Context, dB database.DBI, treadConfig *models.AssetTyresTreadConfig) error
	UpdateAssetTyresTreadConfig(ctx context.Context, dB database.DBI, id string, treadConfig *models.AssetTyresTreadConfig) error
	DeleteAssetTyresTreadConfig(ctx context.Context, dB database.DBI, id string) error
	GetAssetTyresTreadConfigBrandList(ctx context.Context, dB database.DBI, param models.GetAssetTyresTreadConfigListParam) (int, []models.AssetTyresTreadConfig, error)

	ChartCountRTDStatus(ctx context.Context, dB database.DBI, param models.AssetTyreChartParam) ([]commonmodel.Chart, error)

	GetUtilizationRatePercentageStatus(ctx context.Context, dB database.DBI) ([]models.UtilizationRatePercentageStatus, error)
	GetAssetTyreInstalledReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreInstalledReportParam) (int, []models.AssetTyre, error)
	GetAssetTyreInstalledReportExport(ctx context.Context, dB database.DBI, cond models.AssetTyreInstalledCondition) ([]models.AssetTyre, error)
	GetAssetTyreUninstalledReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreUninstalledReportParam) (int, []models.AssetLinked, error)
	GetAssetTyreUninstalledReportExport(ctx context.Context, dB database.DBI, param models.GetAssetTyreUninstalledReportParam) ([]models.AssetLinked, error)

	GetAssetTyreReplacementForecastReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreReplacementForecastReportParam) (int, []models.AssetTyre, error)
	GetAssetTyreReplacementForecastReportExport(ctx context.Context, dB database.DBI, param models.GetAssetTyreReplacementForecastReportParam) ([]models.AssetTyre, error)

	GetAssetTyreUsageReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreUsageReportParam) (int, []models.AssetTyre, error)
	GetAssetTyreUsageReportExport(ctx context.Context, dB database.DBI, param models.GetAssetTyreUsageReportParam) ([]models.AssetTyre, error)

	GetAssetTyreInspectionReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreInspectionReportParam) (int, []models.AssetTyre, error)
	GetAssetTyreInspectionReportExport(ctx context.Context, dB database.DBI, param models.GetAssetTyreInspectionReportParam) ([]models.AssetTyre, error)

	GetAssetTyreRotationReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreRotationReportParam) (int, []models.TyreRotationReportModel, error)
	GetAssetTyreRotationReportExport(ctx context.Context, dB database.DBI, param models.GetAssetTyreRotationReportParam) ([]models.TyreRotationReportModel, error)
}
