package routers

import (
	"assetfindr/internal/app/asset/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterLocationRoutes(route *gin.Engine, locationHandler *handler.LocationHandler) *gin.Engine {

	locationRoutes := route.Group("/v1/locations", middleware.TokenValidationMiddleware())
	{
		locationRoutes.GET("", locationHandler.GetLocations)
		locationRoutes.GET("/:id", locationHandler.GetLocation)
		locationRoutes.POST("", locationHandler.CreateLocation)
		locationRoutes.PUT("/:id", locationHandler.UpdateLocation)
		locationRoutes.DELETE("/:id", locationHandler.DeleteLocation)

	}
	return route
}
