package routers

import (
	"assetfindr/internal/app/asset/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterAssetVehicleRoutes(route *gin.Engine, assetVehicleHandler *handler.AssetVehicleHandler) *gin.Engine {
	// TODO: change to "asset-fleetoptimax" instead of "assets-vehicles"
	asetVehicleRoutes := route.Group("/v1/assets-vehicles", middleware.TokenValidationMiddleware())
	{
		asetVehicleRoutes.GET("", assetVehicleHandler.GetAssetVehicles)
		asetVehicleRoutes.GET("/:id", assetVehicleHandler.GetAssetVehicleByID)

		asetVehicleRoutes.POST("", assetVehicleHandler.CreateAssetVehicle)
		asetVehicleRoutes.PUT("/vehicle-km", assetVehicleHandler.UpdateAssetVehicleKm)
		asetVehicleRoutes.PUT("/vehicle-meter", assetVehicleHandler.UpdateAssetVehicleMeter)
		asetVehicleRoutes.PUT("/:asset_id", assetVehicleHandler.UpdateAssetVehicle)
		asetVehicleRoutes.PUT("/:asset_id/axle-configuration", assetVehicleHandler.UpdateAssetVehicleAxleConfiguration)
		asetVehicleRoutes.POST("/bulk", assetVehicleHandler.BulkUloadAssetVehicles)
		asetVehicleRoutes.GET("/stats-histories/:asset_id/last-month", assetVehicleHandler.GetLastMonthAssetVehicleStatsHistory)
	}

	asetVehicleAdminRoutes := route.Group("/v1/admin/asset-vehicles", middleware.APITokenMiddleware)
	{
		asetVehicleAdminRoutes.POST("/populate-stats-histories", assetVehicleHandler.PopulatePeriodicAssetVehicleStatsHistories)
	}

	vehicleRoutes := route.Group("/v1/vehicles", middleware.TokenValidationMiddleware())
	{
		vehicleRoutes.GET("", assetVehicleHandler.GetVehicles)
		vehicleRoutes.GET("/:id", assetVehicleHandler.GetVehicle)
		vehicleRoutes.POST("", assetVehicleHandler.CreateVehicle)
		vehicleRoutes.DELETE("/:id", assetVehicleHandler.DeleteVehicle)
		vehicleRoutes.PUT("/:id", assetVehicleHandler.UpdateVehicle)
		vehicleRoutes.GET("/export", assetVehicleHandler.GetVehicleCSV)
	}

	return route
}
