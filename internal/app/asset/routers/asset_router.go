package routers

import (
	"assetfindr/internal/app/asset/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterAssetRoutes(route *gin.Engine, assetHandler *handler.AssetHandler) *gin.Engine {

	userRoutes := route.Group("/v1/assets", middleware.TokenValidationMiddleware())
	{
		userRoutes.GET("/all", assetHandler.GetAssets)
		userRoutes.GET("/selections", assetHandler.GetAssetsSelections)

		userRoutes.PUT("/:asset_id/status", assetHandler.UpdateAssetStatus)
		userRoutes.GET("/brands", assetHandler.GetAssetBrands)
		userRoutes.GET("/:asset_id/logs", assetHandler.GetAssetLogs)

		userRoutes.POST("/rfid/is-exist", assetHandler.RfidIsExists)
		userRoutes.POST("/serial-number/is-exist", assetHandler.SerialNumberIsExist)
		userRoutes.POST("/reference-number/is-exist", assetHandler.ReferenceNumberIsExist)

		userRoutes.GET("/counts", assetHandler.GetAssetCount)
	}

	assetChartRoute := route.Group("/v1/assets/charts", middleware.TokenValidationMiddleware())
	{
		assetChartRoute.GET("/asset-status", assetHandler.ChartAssetStatus)
		assetChartRoute.GET("/asset-brands", assetHandler.ChartAssetBrands)
		assetChartRoute.GET("/asset-custom-categories", assetHandler.ChartAssetCustomCategories)
		assetChartRoute.GET("/asset-custom-categories-subcategories", assetHandler.ChartAssetCustomCategoriesSubcategories)
		assetChartRoute.GET("/total-asset", assetHandler.ChartTotalAssets)
		assetChartRoute.GET("/expired-component", assetHandler.ChartAssetExpiredComponent)

		tyresGroup := assetChartRoute.Group("/tyres")
		{
			tyresGroup.GET("/status", assetHandler.ChartTyreStatus)
			tyresGroup.GET("/number", assetHandler.ChartTyreNumber)
			tyresGroup.GET("/thread-status", assetHandler.ChartTyreTread)
			tyresGroup.GET("/size", assetHandler.ChartTyreSize)
			tyresGroup.GET("/brand", assetHandler.ChartTyreBrand)
		}

		installedTyresGroup := assetChartRoute.Group("/installed-tyres")
		{
			installedTyresGroup.GET("/total-inspections", assetHandler.ChartInstalledTyreTotalInspectionToDate)
			installedTyresGroup.GET("/inspection-types", assetHandler.ChartCountInspectionTyreType)
			installedTyresGroup.GET("/inspected", assetHandler.ChartCountInstalledTyreInspected)
			installedTyresGroup.GET("/not-inspected", assetHandler.ChartCountInstalledTyreNotInspected)
			installedTyresGroup.GET("/rtd-status", assetHandler.ChartCountRTDStatus)
			installedTyresGroup.GET("/rtd-status/critical", assetHandler.ChartCountRTDStatusCritical)
			installedTyresGroup.GET("/with-sensor", assetHandler.ChartCountInstalledTyreWithSensor)
		}
	}

	assetManagementRoutes := route.Group("/v1/asset-managements", middleware.TokenValidationMiddleware())
	{
		assetManagementRoutes.GET("", assetHandler.GetListAssetManagement)
		assetManagementRoutes.POST("", assetHandler.CreateAssetManagement)
		assetManagementRoutes.GET("/:id", assetHandler.GetAssetManagement)
		assetManagementRoutes.PUT("/:id", assetHandler.UpdateAssetManagement)
	}

	convertRoutes := route.Group("/v1/converts", middleware.TokenValidationMiddleware())
	{
		convertRoutes.POST("/to-optimax", assetHandler.ConvertToOptimax)
		convertRoutes.POST("/to-general", assetHandler.ConvertFromOptimax)
		convertRoutes.PUT("/tyre-optimax/enable", assetHandler.EnableTyreOptimax)
		convertRoutes.PUT("/tyre-optimax/disable", assetHandler.DisableTyreOptimax)
	}
	assetCategoryRoutes := route.Group("/v1/asset-categories", middleware.TokenValidationMiddleware())
	{
		assetCategoryRoutes.GET("", assetHandler.GetAssetCategories)
	}

	assetSubCategoryRoutes := route.Group("/v1/asset-sub-categories", middleware.TokenValidationMiddleware())
	{
		assetSubCategoryRoutes.GET("", assetHandler.GetAssetSubCategories)
	}

	asetAdminRoutes := route.Group("/v1/admin/assets", middleware.TokenValidationMiddleware(), middleware.AdminValidationMiddleware())
	{
		asetAdminRoutes.GET("", assetHandler.GetAssetsByAdmin)
	}

	return route
}
