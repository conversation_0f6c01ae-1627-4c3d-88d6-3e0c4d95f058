package routers

import (
	"assetfindr/internal/app/asset/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterVehicleTargetTyreRemovalRoutes(route *gin.Engine, vehicleTargetTyreRemovalHandler *handler.VehicleTargetTyreRemovalHandler) *gin.Engine {
	vehicleTargetTyreRemovalRoutes := route.Group("/v1/vehicle-target-tyre-removals", middleware.TokenValidationMiddleware())
	{
		vehicleTargetTyreRemovalRoutes.POST("", vehicleTargetTyreRemovalHandler.CreateVehicleTargetTyreRemoval)
		vehicleTargetTyreRemovalRoutes.PUT("/:id", vehicleTargetTyreRemovalHandler.UpdateVehicleTargetTyreRemoval)
		vehicleTargetTyreRemovalRoutes.GET("", vehicleTargetTyreRemovalHandler.GetVehicleTargetTyreRemovalList)
		vehicleTargetTyreRemovalRoutes.GET("/:id", vehicleTargetTyreRemovalHandler.GetVehicleTargetTyreRemovalByID)
		vehicleTargetTyreRemovalRoutes.POST("/availability", vehicleTargetTyreRemovalHandler.CheckTargetRTDAvailability)
	}

	return route
}
