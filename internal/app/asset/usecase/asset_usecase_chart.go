package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	integrationConstant "assetfindr/internal/app/integration/constants"
	integrationModels "assetfindr/internal/app/integration/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"

	"gopkg.in/guregu/null.v4"
)

func (uc *AssetUseCase) ChartAssetStatus(ctx context.Context, req dtos.AssetStatusChartRequest) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	var statusCodes []string
	if len(req.StatusCodes) > 0 {
		statusCodes = req.StatusCodes
	}
	if req.StatusCode != "" {
		statusCodes = append(statusCodes, req.StatusCode)
	}

	charts, err := uc.AssetRepository.ChartAssetStatus(ctx, uc.DB.DB(), claim.GetLoggedInClientID(), statusCodes)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartAssetBrands(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetRepository.ChartAssetBrands(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	notSetCharts, err := uc.AssetRepository.ChartNotSetAssetBrands(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	for _, notSetChart := range notSetCharts {
		if notSetChart.Y > 0 {
			charts = append(charts, notSetChart)
		}
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartAssetCustomCategories(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetRepository.ChartAssetCustomCategories(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartAssetCustomCategoriesSubcategories(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	categoriesCharts, err := uc.AssetRepository.ChartAssetCustomCategories(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	subcategoriesCharts, err := uc.AssetRepository.ChartAssetCustomCategoriesSubcategories(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	notSetSubcategoryChartMap := map[string]float64{}
	for _, chart := range categoriesCharts {
		totalSetSubcategories := 0.0
		for _, subcategoryChart := range subcategoriesCharts {
			if subcategoryChart.ParentCode == chart.Code.String {
				totalSetSubcategories += subcategoryChart.Y
			}
		}

		totalNotSetSubcategories := chart.Y - totalSetSubcategories
		if totalNotSetSubcategories > 0 {
			notSetSubcategoryChartMap[chart.Code.String] = totalNotSetSubcategories
		}
	}

	finalNotSetSubcategoriesCharts := []commonmodel.Chart{}
	for parentCode, Y := range notSetSubcategoryChartMap {
		finalNotSetSubcategoriesCharts = append(finalNotSetSubcategoriesCharts, commonmodel.Chart{
			Name:       "Not Set",
			Code:       null.StringFrom("NOT_SET"),
			Y:          Y,
			ParentCode: parentCode,
		})
	}

	var charts []commonmodel.Chart
	charts = append(charts, categoriesCharts...)
	charts = append(charts, subcategoriesCharts...)
	if len(finalNotSetSubcategoriesCharts) > 0 {
		charts = append(charts, finalNotSetSubcategoriesCharts...)
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartTotalAssets(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetRepository.ChartCountAssets(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

// Tyre Chart Usecase

func (uc *AssetUseCase) ChartTyreStatus(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetRepository.ChartTyreStatus(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartTyreNumber(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, err := uc.AssetRepository.CountAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ClientID:         claim.GetLoggedInClientID(),
			Categories:       []string{constants.ASSET_CATEGORY_TYRE_CODE},
			ExcludedStatuses: []string{constants.ASSET_STATUS_CODE_DISPOSED, constants.ASSET_STATUS_CODE_SCRAPED},
		},
	})
	if err != nil {
		return nil, err
	}

	charts := []commonmodel.Chart{
		{
			Y:    float64(count),
			Name: "Total Tyre",
		},
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartTyreTread(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetRepository.ChartTyreTread(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartTyreSize(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetRepository.ChartTyreSize(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartTyreBrand(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetRepository.ChartTyreBrand(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartInstalledTyreTotalInspectionToDate(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.assetInspectionTypeRepo.ChartTotalInspectionToDate(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartCountInspectionTyreType(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.assetInspectionTypeRepo.ChartCountInspectionTyreType(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartCountInstalledTyreInspected(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.assetInspectionTypeRepo.ChartCountInstalledTyreInspected(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartCountInstalledTyreNotInspected(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.assetInspectionTypeRepo.ChartCountInstalledTyreNotInspected(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartCountRTDStatus(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetTyreRepo.ChartCountRTDStatus(ctx, uc.DB.DB(), models.AssetTyreChartParam{
		Where: models.AssetTyreWhere{
			ClientID: claim.GetLoggedInClientID(),
		},
	})

	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetUseCase) ChartCountRTDStatusCritical(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetTyreRepo.ChartCountRTDStatus(ctx, uc.DB.DB(), models.AssetTyreChartParam{
		Where: models.AssetTyreWhere{
			ClientID:                   claim.GetLoggedInClientID(),
			IsCriticalOnLinkedNonSpare: true,
		},
	})

	if err != nil {
		return nil, err
	}

	chart := commonmodel.Chart{
		Name: constants.TYRE_RTD_STATUS_CONDITION_CRITICAL_LABEL,
		Code: null.StringFrom(constants.TYRE_RTD_STATUS_CONDITION_CRITICAL),
		Y:    0,
	}

	for _, c := range charts {
		if c.Code.String == constants.TYRE_RTD_STATUS_CONDITION_CRITICAL {
			chart.Y = c.Y
		}
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        []commonmodel.Chart{chart},
	}, nil
}

func (uc *AssetUseCase) ChartCountInstalledTyreWithSensor(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integrations, err := uc.IntegrationRepo.GetIntegrations(ctx, uc.DB.DB(), integrationModels.IntegrationCondition{
		Where: integrationModels.IntegrationWhere{
			ClientID: claim.GetLoggedInClientID(),
			Status:   integrationConstant.INTEGRATION_STATUS_CODE_ACTIVE,
		},
		Columns: []string{"internal_reference_id"},
	})
	if err != nil {
		return nil, err
	}

	assetIDs := make([]string, 0, len(integrations))
	for _, integration := range integrations {
		assetIDs = append(assetIDs, integration.InternalReferenceID)
	}

	chart := commonmodel.Chart{
		Name: "Tyre Installed With Sensor",
	}

	if len(assetIDs) > 0 {
		total, err := uc.AssetRepository.CountAsset(ctx, uc.DB.DB(), models.AssetCondition{
			Where: models.AssetWhere{
				IDs:        assetIDs,
				ClientID:   claim.GetLoggedInClientID(),
				Categories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
				Statuses:   []string{constants.ASSET_STATUS_CODE_INSTALLED},
			},
		})
		if err != nil {
			return nil, err
		}

		chart.Y = float64(total)
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data: []commonmodel.Chart{
			chart,
		},
	}, nil

}

func (uc *AssetUseCase) ChartAssetExpiredComponent(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetRepository.ChartAssetExpiredComponent(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}
