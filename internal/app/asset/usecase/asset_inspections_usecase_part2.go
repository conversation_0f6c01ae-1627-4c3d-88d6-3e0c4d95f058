package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	userModels "assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/timehelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"assetfindr/pkg/common/helpers/tyrecalculationutils"
	"context"
	"time"

	notifConstants "assetfindr/internal/app/notification/constants"
	notificationConstant "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"

	"html/template"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

func (uc *AssetInspectionUseCase) notifyAfterAddInspectionChildern(
	ctx context.Context,
	inspectionID string,
) {

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return
	}

	inspectionVehicle, err := uc.AssetInspectionVehicleRepository.GetAssetInspectionVehicle(ctx, uc.DB.DB(), models.AssetInspectionVehicleCondition{
		Where: models.AssetInspectionVehicleWhere{
			InspectionID: inspectionID,
		},
		Preload: models.AssetInspectionVehiclePreload{
			Asset:           true,
			AssetInspection: true,
		},
		Columns: []string{},
	})
	if err != nil {
		return
	}

	// DON'T SEND NOTIFICATION IF VEHICLE ID IS NULL
	if inspectionVehicle.AssetVehicleID == "" {
		return
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: inspectionVehicle.ClientID,
		},
	})
	if err != nil {
		return
	}

	targetURL := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notificationConstant.DESTINATION_TYPE_INSPECTION, inspectionID)

	loc, _ := time.LoadLocation("Asia/Jakarta")

	assetSerialOrRef := inspectionVehicle.Asset.SerialNumber
	if inspectionVehicle.Asset.ReferenceNumber != "" {
		assetSerialOrRef = inspectionVehicle.Asset.ReferenceNumber
	}

	templ := tmplhelpers.CreateInspectionTyre{
		AssetName:      inspectionVehicle.Asset.Name,
		AssetIdent:     assetSerialOrRef,
		InspectionNo:   inspectionVehicle.AssetInspection.InspectionNumber,
		InspectionDate: inspectionVehicle.AssetInspection.CreatedAt.In(loc).Format(timehelpers.RFC1123Notif),
		InspectorName:  claim.GetName(),
		RedirectLink:   template.URL(targetURL),
	}

	assetAssignment, err := uc.assetAssignmentRepo.GetAssetAssignment(ctx, uc.DB.DB(), models.AssetAssignmentCondition{
		Where: models.AssetAssignmentWhere{
			AssetID:  inspectionVehicle.AssetVehicleID,
			Assigned: true,
		},
	})
	if err != nil {
		return
	}

	assetAssignmentGroups, err := uc.assetAssignmentRepo.GetAssetAssignmentGroup(ctx, uc.DB.DB(), models.AssetAssignmentCondition{
		Where: models.AssetAssignmentWhere{
			AssetID: inspectionVehicle.AssetVehicleID,
		},
	})
	if err != nil {
		return
	}

	createNotifReq := notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{},
		SendToEmail:     true,
		SendToPushNotif: true,
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            assetAssignment.UserID,
		SourceCode:        notificationConstant.NOTIFICATION_SOURCE_CODE_INSPECTION,
		SourceReferenceID: inspectionID,
		TargetReferenceID: "",
		TargetURL:         targetURL,
		MessageHeader:     templ.GenerateEmailSubject(),
		MessageBody:       templ.GenerateEmailBody(),
		ClientID:          inspectionVehicle.ClientID,
		TypeCode:          notificationConstant.NOTIFICATION_TYPE_USER_ACTIVITY_CODE,
		ContentTypeCode:   "",
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templ.GeneratePushNotifSubject(),
			Body:  templ.GeneratePushNotifBody(),
		},
		ReferenceCode:  notifConstants.NOTIF_REF_INSPECTION,
		ReferenceValue: inspectionID,
	}
	createNotifReq.Items = append(createNotifReq.Items, notifItem)

	for i := range assetAssignmentGroups {
		if assetAssignmentGroups[i].UserID == assetAssignment.UserID {
			continue
		}

		notifItem.UserID = assetAssignmentGroups[i].UserID
		createNotifReq.Items = append(createNotifReq.Items, notifItem)
	}

	go func(req notificationDtos.CreateNotificationReq) {
		_ = uc.notifUseCase.CreateNotification(
			contexthelpers.WithoutCancel(ctx), req,
		)
	}(createNotifReq)

}

func (uc *AssetInspectionUseCase) calculateInspectionTyrePressureStatusAndRTDMismacth(
	ctx context.Context,
	inspectionID string,
	tx database.DBUsecase,
) error {

	vehicleInspection, err := uc.AssetInspectionVehicleRepository.GetAssetInspectionVehicle(ctx, tx.DB(), models.AssetInspectionVehicleCondition{
		Where: models.AssetInspectionVehicleWhere{
			InspectionID: inspectionID,
		},
		Preload: models.AssetInspectionVehiclePreload{
			AssetInspectionTyres:          true,
			AssetInspectionTyresAssetTyre: true,
		},
	})
	if err != nil {
		return err
	}

	if vehicleInspection.AssetVehicleID == "" {
		return nil
	}

	if len(vehicleInspection.AssetInspectionTyres) == 0 {
		return nil
	}

	if vehicleInspection.AxleConfiguration.Status == pgtype.Null ||
		vehicleInspection.AxleConfiguration.Status == pgtype.Undefined {
		return nil
	}

	axleConfig, err := models.AxleConfigurationJSONBToStruct(vehicleInspection.AxleConfiguration)
	if err != nil {
		return err
	}

	if len(axleConfig) == 0 {
		return nil
	}

	mapPositionToPressureConfig := models.MapPositionToPressureFromAxle(axleConfig)

	mapTyreInspectionByPos := map[int]models.AssetInspectionTyre{}
	for _, assetInspectionTyre := range vehicleInspection.AssetInspectionTyres {
		if assetInspectionTyre.AssetTyreID == "" {
			continue
		}
		mapTyreInspectionByPos[int(assetInspectionTyre.TyrePosition)] = assetInspectionTyre
		if assetInspectionTyre.Pressure == 0 {
			continue
		}

		pressureConfig := mapPositionToPressureConfig[int(assetInspectionTyre.TyrePosition)]
		if !pressureConfig.PressureMin.Valid || !pressureConfig.PressureMax.Valid {
			continue
		}

		pressureStatusCode := constants.ASSET_TYRE_PRESSURE_CODE_NORMAL
		if int(pressureConfig.PressureMin.Int64) > int(assetInspectionTyre.Pressure) {
			pressureStatusCode = constants.ASSET_TYRE_PRESSURE_CODE_UNDERFLATED
		} else if int(pressureConfig.PressureMax.Int64) < int(assetInspectionTyre.Pressure) {
			pressureStatusCode = constants.ASSET_TYRE_PRESSURE_CODE_OVERFLATED
		}

		err := uc.AssetInspectionTyreRepository.UpdateAssetInspectionTyre(ctx, tx.DB(),
			assetInspectionTyre.ID,
			&models.AssetInspectionTyre{
				PressureStatusCode: pressureStatusCode,
			})
		if err != nil {
			return err
		}
	}

	if vehicleInspection.MaxRtdDiffTolerance != nil && vehicleInspection.MaxRtdDiffTolerance.Valid {
		mapTyrePairByPosition := models.MapTyrePairPositionByAxle(axleConfig)
		for i, j := range mapTyrePairByPosition {
			iAssetInspectionTyre, ok := mapTyreInspectionByPos[i]
			if !ok {
				// case pos i not inspected
				continue
			}

			if iAssetInspectionTyre.AverageRTD == 0 {
				// case pos i avg rtd not inspected
				continue
			}

			jAssetInspectionTyre, ok := mapTyreInspectionByPos[j]
			if !ok {
				continue
			}

			if jAssetInspectionTyre.AverageRTD == 0 {
				continue
			}

			rtdDiff := tyrecalculationutils.CalculateRTDDiff(
				iAssetInspectionTyre.AverageRTD,
				jAssetInspectionTyre.AverageRTD,
				iAssetInspectionTyre.AssetTyre.StartThreadDepth,
				jAssetInspectionTyre.AssetTyre.StartThreadDepth)

			isMismatch := null.BoolFrom(false)
			if rtdDiff > float64(vehicleInspection.MaxRtdDiffTolerance.ValueOrZero()) {
				isMismatch = null.BoolFrom(true)
			}

			err := uc.AssetInspectionTyreRepository.UpdateAssetInspectionTyre(ctx, tx.DB(),
				iAssetInspectionTyre.ID,
				&models.AssetInspectionTyre{
					IsMismatch: isMismatch,
				})
			if err != nil {
				return err
			}

			err = uc.AssetInspectionTyreRepository.UpdateAssetInspectionTyre(ctx, tx.DB(),
				jAssetInspectionTyre.ID,
				&models.AssetInspectionTyre{
					IsMismatch: isMismatch,
				})
			if err != nil {
				return err
			}

		}
	}

	err = uc.AssetLinkedUseCase.calculateLinkedTyreRTDMismatch(ctx, vehicleInspection.AssetVehicleID, tx)
	if err != nil {
		return err
	}

	return nil
}

func (uc *AssetInspectionUseCase) GetLastInspectionByDigispectVehicle(ctx context.Context, digispectVehicleID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if digispectVehicleID == "" {
		return &commonmodel.DetailResponse{
			Data:        nil,
			Success:     true,
			Message:     "Success",
			ReferenceID: "",
		}, nil
	}

	inspection, err := uc.AssetInspectionRepository.GetAssetInspection(ctx, uc.DB.DB(), models.AssetInspectionCondition{
		Where: models.AssetInspectionWhere{
			DigiSpectVehicleID: digispectVehicleID,
			ClientID:           claim.GetLoggedInClientID(),
		},
		Preload: models.AssetInspectionPreload{
			AssetInspectionTyres:   true,
			AssetInspectionVehicle: true,
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if inspection == nil {
		return &commonmodel.DetailResponse{
			Data:        nil,
			Success:     true,
			Message:     "Success",
			ReferenceID: "",
		}, nil
	}

	resp := dtos.GetLastInspectionByDigispectVehicle{
		Vehicle: dtos.BuildGetLastInspectionVehicleByDigispectVehicle(inspection.AssetInspectionVehicle),
		Tyres:   []dtos.GetLastInspectionTyresByDigispectVehicle{},
	}

	for _, inspectionTyre := range inspection.AssetInspectionTyres {
		resp.Tyres = append(resp.Tyres, dtos.BuildGetLastInspectionTyresByDigispectVehicle(inspectionTyre))
	}

	return &commonmodel.DetailResponse{
		Data:        resp,
		Success:     true,
		Message:     "Success",
		ReferenceID: inspection.ID,
	}, nil
}
