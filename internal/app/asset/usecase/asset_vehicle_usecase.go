package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/errorconstants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/app/asset/utils"
	financeConstants "assetfindr/internal/app/finance/constants"
	financeDtos "assetfindr/internal/app/finance/dtos"
	financeRepo "assetfindr/internal/app/finance/repository"
	financeUsecase "assetfindr/internal/app/finance/usecase"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storageDtos "assetfindr/internal/app/storage/dtos"
	uploadConstants "assetfindr/internal/app/upload/constants"
	uploadDto "assetfindr/internal/app/upload/dtos"
	uploadModel "assetfindr/internal/app/upload/models"
	uploadRepo "assetfindr/internal/app/upload/repository"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	internalConstants "assetfindr/internal/constants"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/sqlhelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"bufio"
	"context"
	"errors"
	"fmt"
	"html/template"
	"io"
	"os"
	"time"

	storageConstants "assetfindr/internal/app/storage/constants"
	storageUseCase "assetfindr/internal/app/storage/usecase"

	userIdentityRepository "assetfindr/internal/app/user-identity/repository"

	"github.com/gocarina/gocsv"
	"gopkg.in/guregu/null.v4"

	"github.com/jackc/pgtype"
)

type AssetVehicleUseCase struct {
	DB                     database.DBUsecase
	AssetRepository        repository.AssetRepository
	AssetVehicleRepository repository.AssetVehicleRepository
	AssetAssignmentUseCase *AssetAssignmentUseCase
	UserRepository         userIdentityRepository.UserRepository
	AssetLogRepo           repository.AssetLogRepository
	assetAssignmentRepo    repository.AssetAssignmentRepository
	notifUseCase           *notificationUsecase.NotificationUseCase
	financeUseCase         financeUsecase.FinanceUseCase
	attachmentUseCase      *storageUseCase.AttachmentUseCase
	storageUseCase         *storageUseCase.StorageUseCase
	assetLinkedRepo        repository.AssetLinkedRepository
	assetTyreRepo          repository.AssetTyreRepository
	assetUsecase           *AssetUseCase
	uploadRepo             uploadRepo.UploadRepository
	vehicleBodyTypeRepo    repository.AssetVehicleBodyTypeRepository
	brandRepo              repository.BrandRepository
	FinanceRepository      financeRepo.FinanceRepository
}

func NewAssetVehicleUseCase(
	DB database.DBUsecase,
	assetRepo repository.AssetRepository,
	userRepo userIdentityRepository.UserRepository,
	assetVehicleRepository repository.AssetVehicleRepository,
	assetLogRepo repository.AssetLogRepository,
	assetAssignmentRepo repository.AssetAssignmentRepository,
	financeUseCase financeUsecase.FinanceUseCase,
	attachmentUseCase *storageUseCase.AttachmentUseCase,
	assetLinkedRepo repository.AssetLinkedRepository,
	assetTyreRepo repository.AssetTyreRepository,
	assetUsecase *AssetUseCase,
	storageUseCase *storageUseCase.StorageUseCase,
	uploadRepo uploadRepo.UploadRepository,
	vehicleBodyTypeRepo repository.AssetVehicleBodyTypeRepository,
	brandRepo repository.BrandRepository,
) *AssetVehicleUseCase {
	return &AssetVehicleUseCase{
		DB:                     DB,
		AssetRepository:        assetRepo,
		AssetVehicleRepository: assetVehicleRepository,
		AssetAssignmentUseCase: nil,
		UserRepository:         userRepo,
		AssetLogRepo:           assetLogRepo,
		assetAssignmentRepo:    assetAssignmentRepo,
		financeUseCase:         financeUseCase,
		attachmentUseCase:      attachmentUseCase,
		assetLinkedRepo:        assetLinkedRepo,
		assetTyreRepo:          assetTyreRepo,
		assetUsecase:           assetUsecase,
		storageUseCase:         storageUseCase,
		uploadRepo:             uploadRepo,
		vehicleBodyTypeRepo:    vehicleBodyTypeRepo,
		brandRepo:              brandRepo,
		FinanceRepository:      financeUseCase.FinanceRepository,
	}
}

func (uc *AssetVehicleUseCase) SetNotifUseCase(notifUsecase notificationUsecase.NotificationUseCase) {
	uc.notifUseCase = &notifUsecase
}

func (uc *AssetVehicleUseCase) UpdateAssetAssignmentUseCase(assetAssignmentUseCase *AssetAssignmentUseCase) {
	uc.AssetAssignmentUseCase = assetAssignmentUseCase
}

func (uc *AssetVehicleUseCase) GetAssetVehicles(ctx context.Context, req dtos.AssetVehicleListReq) (commonmodel.ListResponse, error) {
	assetsResponse := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return assetsResponse, err
	}

	isViewAllAsset := claim.IsHasPermission(
		constants.ASSET_PERMISSION_CATEGORY,
		constants.ASSET_PERMISSION_VIEW_ALL_LIST_ASSET,
	)
	assignUserID := ""
	if !isViewAllAsset {
		assignUserID = claim.UserID
	}

	totalRecords, assets, err := uc.AssetVehicleRepository.GetAssetVehicleList(ctx, uc.DB.DB(),
		models.GetAssetVehicleListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetVehicleCondition{
				Where: models.AssetVehicleWhere{
					ClientID:             claim.GetLoggedInClientID(),
					StatusCode:           req.StatusCodes,
					SubCategories:        req.SubCategories,
					Brands:               req.Brands,
					Models:               req.Models,
					Categories:           []string{constants.ASSET_CATEGORY_VEHICLE_CODE},
					CustomCategories:     req.CustomCategories,
					CustomSubCategories:  req.CustomSubCategories,
					Locations:            req.Locations,
					CreateOn:             req.CreateOn,
					PartnerOwnerID:       req.PartnerOwnerID,
					UpdatedStartDate:     req.UpdatedStartDate,
					UpdatedEndDate:       req.UpdatedEndDate,
					UseTyreOptimax:       req.UseTyreOptimax,
					HasAxleConfiguration: req.HasAxleConfiguration,
					HasLinkedAssetTyre:   req.HasLinkedAssetTyre,
					UseFleetOptimax:      null.BoolFrom(true),
					AssignUserID:         assignUserID,
				},
				Preload: models.AssetVehiclePreload{
					AssetVehicleBodyType: true,
					AssetBrand:           true,
					AssetAssetStatus:     true,
					Vehicle:              true,
					Category:             true,
					CustomAssetCategory:  true,
					AssetModels:          true,
				},
			},
		},
	)
	if err != nil {
		return assetsResponse, err
	}

	if len(assets) == 0 {
		return assetsResponse, nil
	}

	// Get Asset Assignments
	var assetIds []string
	for _, asset := range assets {
		assetIds = append(assetIds, asset.AssetID)
	}

	assignmentsMapByAssetId := map[string]dtos.AssetAssignmentResponse{}

	err = uc.AssetAssignmentUseCase.GetAssetAssignmentByAssetIds(ctx, &assignmentsMapByAssetId, assetIds)
	if err != nil {
		commonlogger.Errorf("Error in getting asset assignments by assignment ids", err)
		return assetsResponse, err
	}

	newResponse := dtos.BuildAssetVehicleListResponse(assets, assignmentsMapByAssetId)
	assetsResponse = commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         newResponse,
	}

	return assetsResponse, nil
}

func (ai *AssetVehicleUseCase) GetAssetVehicleDetail(ctx context.Context, id string) (*dtos.AssetAssignmentDetailResponse, error) {
	assetVehicle, err := ai.AssetVehicleRepository.GetAssetVehicleByID(ctx, ai.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	userAssignCertificateName := ""
	inspectionBookNumberAssignToName := ""
	vrdNumberAssignToName := ""
	users, err := ai.UserRepository.GetUsersV2(ctx, ai.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			IDs: []string{
				assetVehicle.RegistrationCertificateAssignTo,
				assetVehicle.InspectionBookNumberAssignTo,
				assetVehicle.VrdNumberAssignTo,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	for _, user := range users {
		if user.ID == assetVehicle.InspectionBookNumberAssignTo {
			inspectionBookNumberAssignToName = user.GetName()
		}

		if user.ID == assetVehicle.VrdNumberAssignTo {
			vrdNumberAssignToName = user.GetName()
		}

		if user.ID == assetVehicle.RegistrationCertificateAssignTo {
			userAssignCertificateName = user.GetName()
		}
	}

	refAmounts, err := ai.financeUseCase.GetTotalCostsByReferenceIDs(ctx, []string{id})
	if err != nil {
		return nil, err
	}

	var assetAssignment dtos.AssetAssignmentResponse
	err = ai.AssetAssignmentUseCase.GetAssetAssignmentResponseByAssetId(ctx, &assetAssignment, assetVehicle.AssetID)
	if err != nil {
		return nil, err
	}

	response := dtos.BuildAssetAssignmentDetailResponse(*assetVehicle, assetAssignment)
	response.RegistrationCertificateAssignUserFullName = userAssignCertificateName
	response.InspectionBookNumberAssignToName = inspectionBookNumberAssignToName
	response.VrdNumberAssignToName = vrdNumberAssignToName
	if len(refAmounts) > 0 {
		response.Cost = refAmounts[0].Amount
		response.CostAsset = refAmounts[0].AmountFixedAsset
		response.CostExpense = refAmounts[0].AmountExpense
	}
	return &response, nil
}

func (avu *AssetVehicleUseCase) ValidateCreateAssetVehicle(ctx context.Context, vehicleDTO *dtos.AssetVehicleListReceiver) error {
	if !vehicleDTO.UseHourmeter.ValueOrZero() && !vehicleDTO.UseKilometer.ValueOrZero() {
		return fmt.Errorf("one of use_hourmeter or use_kilometer must be true")
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	if exists, err := avu.AssetVehicleRepository.IsRegistrationNumberExist(ctx, avu.DB.DB(), vehicleDTO.RegistrationNumber, claim.GetLoggedInClientID()); err != nil {
		return err
	} else if exists {
		return errors.New("registration number already exists")
	}

	if exists, err := avu.AssetVehicleRepository.IsEngineNumberExist(ctx, avu.DB.DB(), vehicleDTO.EngineNumber, claim.GetLoggedInClientID()); err != nil {
		return err
	} else if exists {
		return errors.New("engine number already exists")
	}

	return nil
}

func (avu *AssetVehicleUseCase) CreateAssetVehicle(ctx context.Context, vehicleDTO *dtos.AssetVehicleListReceiver) (*models.Asset, error) {
	tx, err := avu.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	asset := &models.Asset{
		Name:                  vehicleDTO.VehicleName,
		BrandID:               vehicleDTO.BrandID,
		ModelNumber:           vehicleDTO.ModelNumber,
		SerialNumber:          vehicleDTO.SerialNumber,
		AssetCategoryCode:     constants.ASSET_CATEGORY_VEHICLE_CODE,
		SubCategoryCode:       constants.ASSET_SUB_CATEGORY_VEHICLE_CODE,
		ProductionDate:        parseDate(fmt.Sprintf("%d-01-01", vehicleDTO.ProductionYear)),
		AssetStatusCode:       vehicleDTO.StatusCode,
		Cost:                  vehicleDTO.Cost,
		OwnershipCategoryCode: constants.ASSET_OWNERSHIP_OWN_CODE,
	}

	err = avu.assetUsecase.AssetWithTimer(ctx, tx.DB(), nil, asset)
	if err != nil {
		return nil, err
	}
	err = avu.AssetRepository.CreateAsset(ctx, tx.DB(), asset)
	if err != nil {
		return nil, err
	}

	vehicle := &models.AssetVehicle{
		AssetID:                         asset.ID,
		RegistrationNumber:              vehicleDTO.RegistrationNumber,
		AssetVehicleBodyTypeID:          vehicleDTO.AssetVehicleBodyTypeCode,
		NumberOfTyres:                   vehicleDTO.NumberOfTyres,
		EngineModel:                     vehicleDTO.EngineModel,
		TransmissionModel:               vehicleDTO.TransmissionModel,
		VrdNumber:                       vehicleDTO.VrdNumber,
		VrdExpiryDate:                   parseDate(vehicleDTO.VrdExpiryDate),
		VrdNumberAssignTo:               vehicleDTO.VrdNumberAssignTo,
		EngineNumber:                    vehicleDTO.EngineNumber,
		ChassisNumber:                   vehicleDTO.ChassisNumber,
		GpsDeviceImei:                   sqlhelpers.NullString(vehicleDTO.GpsDeviceImei),
		RegistrationCertificateNumber:   vehicleDTO.RegistrationCertificateNumber,
		RegistrationCertificateAssignTo: vehicleDTO.RegistrationCertificateAssignTo,
		InspectionBookNumber:            vehicleDTO.InspectionBookNumber,
		InspectionBookExpiryDate:        parseDate(vehicleDTO.InspectionBookExpiryDate),
		InspectionBookNumberAssignTo:    vehicleDTO.InspectionBookNumberAssignTo,
		VehicleKM:                       vehicleDTO.VehicleKM,
		VehicleHm:                       calculationhelpers.Multiply100(vehicleDTO.VehicleHm),
		NumberOfSpareTyres:              null.IntFrom(int64(vehicleDTO.NumberOfSpareTyres)),

		UseKilometer: vehicleDTO.UseKilometer,
		UseHourmeter: vehicleDTO.UseHourmeter,
	}

	err = avu.AssetVehicleRepository.CreateAssetVehicle(ctx, tx.DB(), vehicle)
	if err != nil {
		return nil, err
	}

	assetAssignment := models.AssetAssignment{
		AssetID:            asset.ID,
		UserID:             vehicleDTO.AssingedToUserID,
		AssignedDateTime:   time.Now().In(time.UTC),
		UnassignedDateTime: nil,
		ClientID:           vehicleDTO.ClientID,
	}

	err = avu.AssetAssignmentUseCase.CreateAssetAssignmentV2(ctx, tx.DB(), &assetAssignment)
	if err != nil {
		return nil, err
	}

	err = avu.financeUseCase.CreateJournal(ctx, financeDtos.CreateJournalReq{
		AccountTransactionType: financeConstants.ACCOUNT_TRANSACTION_TYPE_CREATE_VEHICLE_COST,
		Date:                   asset.CreatedAt,
		References: []financeDtos.JournalReference{
			{
				ReferenceID: asset.ID,
				SourceCode:  financeConstants.JOURNAL_SOURCE_ASSET_CODE,
			},
		},
		Notes:    "",
		Amount:   int64(asset.Cost),
		ClientID: claim.GetLoggedInClientID(),
		UserID:   claim.UserID,
	})
	if err != nil {
		return nil, err
	}

	_, err = avu.attachmentUseCase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET,
		SourceReferenceID: asset.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            vehicleDTO.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return asset, nil
}

func (uc *AssetVehicleUseCase) UpdateAssetVehicle(ctx context.Context, assetID string, loginUser string, vehicleDTO *dtos.AssetVehicleListReceiver) (*dtos.AssetListVehicleItemResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	asset, err := uc.AssetRepository.GetAssetByID(ctx, uc.DB.DB(), assetID)
	if err != nil {
		commonlogger.Errorf("error while getting asset", err)
		return nil, err
	}

	assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, uc.DB.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			AssetID: assetID,
		},
	})
	if err != nil {
		commonlogger.Errorf("error while getting asset", err)
		return nil, err
	}

	if assetVehicle.ClientID != loginUser || asset.ClientID != loginUser {
		return nil, errorhandler.ErrNotEligible
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	logMapPrev := map[string]interface{}{}
	logMapNew := map[string]interface{}{}
	updatedVehicle, isAnyVehicleUpdate, err := uc.checkUpdateAssetVehicle(ctx, assetVehicle, vehicleDTO, logMapPrev, logMapNew)
	if err != nil {
		return nil, err
	}

	if isAnyVehicleUpdate {
		err = uc.AssetVehicleRepository.UpdateAssetVehicle(ctx, tX.DB(), updatedVehicle)
		if err != nil {
			return nil, fmt.Errorf("error while update vehicle")
		}
	}

	updatedAsset := &models.Asset{
		Name:            vehicleDTO.VehicleName,
		BrandID:         vehicleDTO.BrandID,
		SerialNumber:    vehicleDTO.SerialNumber,
		AssetStatusCode: vehicleDTO.StatusCode,
		ModelNumber:     vehicleDTO.ModelNumber,
		ProductionDate:  parseDate(fmt.Sprintf("%d-01-01", vehicleDTO.ProductionYear)),
		Cost:            vehicleDTO.Cost,
	}

	isAnyAssetUpdate := checkUpdateAsset(asset, updatedAsset, logMapPrev, logMapNew)
	if isAnyAssetUpdate || isAnyVehicleUpdate {
		err = uc.assetUsecase.AssetWithTimer(ctx, tX.DB(), asset, updatedAsset)
		if err != nil {
			return nil, err
		}
		err = uc.AssetRepository.UpdateAsset(ctx, tX.DB(), updatedAsset)
		if err != nil {
			commonlogger.Errorf("error while update asset: %v\n", err)
			return nil, fmt.Errorf("error while update asset")
		}

		assetLog := models.AssetLog{
			UpdatedByUserID: claim.UserID,
			AssetID:         assetID,
			PreviousValue:   pgtype.JSONB{},
			NewValue:        pgtype.JSONB{},
			ClientID:        asset.ClientID,
			Type:            constants.ASSET_UPDATE_LOG_TYPE_UPDATE,
		}

		assetLog.SetUpdate(logMapPrev, logMapNew)

		err = uc.AssetLogRepo.CreateAssetLogs(ctx, tX.DB(), []models.AssetLog{assetLog})
		if err != nil {
			return nil, err
		}

		go uc.notifyAfterUpdateAssetVehicle(contexthelpers.WithoutCancel(ctx), assetID)
	}

	_, err = uc.attachmentUseCase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET,
		SourceReferenceID: assetID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            vehicleDTO.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		commonlogger.Errorf("Error register user when commit to database: %v\n", err)
		return nil, err
	}

	updatedAt := updatedAsset.UpdatedAt
	if updatedAt.IsZero() {
		updatedAt = asset.UpdatedAt
	}

	response := dtos.AssetListVehicleItemResponse{
		ID:                    assetID,
		CreatedAt:             asset.CreatedAt,
		UpdatedAt:             updatedAt,
		VehicleName:           vehicleDTO.VehicleName,
		BrandID:               vehicleDTO.BrandID,
		BrandName:             vehicleDTO.BrandName,
		ModelNumber:           vehicleDTO.ModelNumber,
		SerialNumber:          vehicleDTO.SerialNumber,
		CategoryCode:          vehicleDTO.CategoryCode,
		ProductionYear:        vehicleDTO.ProductionYear,
		StatusCode:            vehicleDTO.StatusCode,
		Status:                vehicleDTO.Status,
		RegistrationNumber:    vehicleDTO.RegistrationNumber,
		VehicleBodyType:       vehicleDTO.VehicleBodyType,
		NumberOfTyres:         vehicleDTO.NumberOfTyres,
		AssingedToUserID:      vehicleDTO.AssingedToUserID,
		AttachedToParentAsset: vehicleDTO.AttachedToParentAsset,
		VehicleKM:             vehicleDTO.VehicleKM,
		NumberOfSpareTyres:    vehicleDTO.NumberOfSpareTyres,
	}

	return &response, nil
}

func (uc *AssetVehicleUseCase) checkUpdateAssetVehicle(
	ctx context.Context,
	vehicle *models.AssetVehicle,
	vehicleDTO *dtos.AssetVehicleListReceiver,
	logMapPrev map[string]interface{},
	logMapNew map[string]interface{},
) (*models.AssetVehicle, bool, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, false, err
	}

	isAnyUpdate := false
	updatedVehicle := &models.AssetVehicle{}
	updatedVehicle.AssetID = vehicle.AssetID
	if vehicle.RegistrationNumber != vehicleDTO.RegistrationNumber {
		exists, err := uc.AssetVehicleRepository.IsRegistrationNumberExist(ctx, uc.DB.DB(), vehicleDTO.RegistrationNumber, claim.GetLoggedInClientID())
		if err != nil {
			return nil, false, err
		}

		if exists {
			return nil, false, errorhandler.ErrDataAlreadyExist("registration number")
		}

		isAnyUpdate = true
		updatedVehicle.RegistrationNumber = vehicleDTO.RegistrationNumber
		field := "Registration number"
		logMapPrev[field] = vehicle.RegistrationNumber
		logMapNew[field] = updatedVehicle.RegistrationNumber
	}

	if vehicle.AssetVehicleBodyTypeID != vehicleDTO.AssetVehicleBodyTypeCode {
		isAnyUpdate = true
		updatedVehicle.AssetVehicleBodyTypeID = vehicleDTO.AssetVehicleBodyTypeCode
		field := "Asset vehicle body type"
		logMapPrev[field] = vehicle.AssetVehicleBodyTypeID
		logMapNew[field] = updatedVehicle.AssetVehicleBodyTypeID
	}

	if vehicle.NumberOfTyres != vehicleDTO.NumberOfTyres {
		isAnyUpdate = true
		updatedVehicle.NumberOfTyres = vehicleDTO.NumberOfTyres
		field := "Number of tyres"
		logMapPrev[field] = vehicle.NumberOfTyres
		logMapNew[field] = updatedVehicle.NumberOfTyres
	}

	if int(vehicle.NumberOfSpareTyres.Int64) != vehicleDTO.NumberOfSpareTyres {
		isAnyUpdate = true
		updatedVehicle.NumberOfSpareTyres = null.IntFrom(int64(vehicleDTO.NumberOfSpareTyres))
		field := "Number of spare tyres"
		logMapPrev[field] = vehicle.NumberOfSpareTyres
		logMapNew[field] = updatedVehicle.NumberOfSpareTyres
	}

	if vehicle.EngineModel != vehicleDTO.EngineModel {
		isAnyUpdate = true
		updatedVehicle.EngineModel = vehicleDTO.EngineModel
		field := "Engine model"
		logMapPrev[field] = vehicle.EngineModel
		logMapNew[field] = updatedVehicle.EngineModel
	}

	if vehicle.TransmissionModel != vehicleDTO.TransmissionModel {
		isAnyUpdate = true
		updatedVehicle.TransmissionModel = vehicleDTO.TransmissionModel
		field := "Transmission model"
		logMapPrev[field] = vehicle.TransmissionModel
		logMapNew[field] = updatedVehicle.TransmissionModel
	}

	if vehicle.VrdNumber != vehicleDTO.VrdNumber {
		isAnyUpdate = true
		updatedVehicle.VrdNumber = vehicleDTO.VrdNumber
		field := "STNK"
		logMapPrev[field] = vehicle.VrdNumber
		logMapNew[field] = updatedVehicle.VrdNumber
	}

	if vehicleDTO.VrdExpiryDate != vehicle.VrdExpiryDate.Format(time.DateOnly) {
		isAnyUpdate = true
		updatedVehicle.VrdExpiryDate = parseDate(vehicleDTO.VrdExpiryDate)
		field := "STNK Expire"
		logMapPrev[field] = vehicle.VrdExpiryDate
		logMapNew[field] = updatedVehicle.VrdExpiryDate
	}

	if vehicle.EngineNumber != vehicleDTO.EngineNumber {
		exists, err := uc.AssetVehicleRepository.IsEngineNumberExist(ctx, uc.DB.DB(), vehicleDTO.EngineNumber, claim.GetLoggedInClientID())
		if err != nil {
			return nil, false, err
		}

		if exists {
			return nil, false, errorhandler.ErrDataAlreadyExist("engine number")
		}

		isAnyUpdate = true
		updatedVehicle.EngineNumber = vehicleDTO.EngineNumber
		field := "Engine number"
		logMapPrev[field] = vehicle.EngineNumber
		logMapNew[field] = updatedVehicle.EngineNumber
	}

	if vehicle.ChassisNumber != vehicleDTO.ChassisNumber {
		isAnyUpdate = true
		updatedVehicle.ChassisNumber = vehicleDTO.ChassisNumber
		field := "Chassis number"
		logMapPrev[field] = vehicle.ChassisNumber
		logMapNew[field] = updatedVehicle.ChassisNumber
	}

	if vehicle.GpsDeviceImei.String != vehicleDTO.GpsDeviceImei {
		isAnyUpdate = true
		updatedVehicle.GpsDeviceImei = sqlhelpers.NullString(vehicleDTO.GpsDeviceImei)
		field := "Gps device imei"
		logMapPrev[field] = vehicle.GpsDeviceImei
		logMapNew[field] = updatedVehicle.GpsDeviceImei
	}

	if vehicle.RegistrationCertificateNumber != vehicleDTO.RegistrationCertificateNumber {
		isAnyUpdate = true
		updatedVehicle.RegistrationCertificateNumber = vehicleDTO.RegistrationCertificateNumber
		field := "BPKB"
		logMapPrev[field] = vehicle.RegistrationCertificateNumber
		logMapNew[field] = updatedVehicle.RegistrationCertificateNumber
	}

	if vehicle.VrdNumberAssignTo != vehicleDTO.VrdNumberAssignTo {
		isAnyUpdate = true
		updatedVehicle.VrdNumberAssignTo = vehicleDTO.VrdNumberAssignTo
		field := "STNK assign to"
		logMapPrev[field] = vehicle.VrdNumberAssignTo
		logMapNew[field] = updatedVehicle.VrdNumberAssignTo
	}

	if vehicle.RegistrationCertificateAssignTo != vehicleDTO.RegistrationCertificateAssignTo {
		isAnyUpdate = true
		updatedVehicle.RegistrationCertificateAssignTo = vehicleDTO.RegistrationCertificateAssignTo
		field := "BPKB assign to"
		logMapPrev[field] = vehicle.RegistrationCertificateAssignTo
		logMapNew[field] = updatedVehicle.RegistrationCertificateAssignTo
	}

	if vehicle.InspectionBookNumber != vehicleDTO.InspectionBookNumber {
		isAnyUpdate = true
		updatedVehicle.InspectionBookNumber = vehicleDTO.InspectionBookNumber
		field := "Inspection book number"
		logMapPrev[field] = vehicle.InspectionBookNumber
		logMapNew[field] = updatedVehicle.InspectionBookNumber
	}

	if vehicle.InspectionBookNumberAssignTo != vehicleDTO.InspectionBookNumberAssignTo {
		isAnyUpdate = true
		updatedVehicle.InspectionBookNumberAssignTo = vehicleDTO.InspectionBookNumberAssignTo
		field := "Inspection book number assign to"
		logMapPrev[field] = vehicle.InspectionBookNumberAssignTo
		logMapNew[field] = updatedVehicle.InspectionBookNumberAssignTo
	}

	if vehicleDTO.InspectionBookExpiryDate != vehicle.InspectionBookExpiryDate.Format(time.DateOnly) {
		isAnyUpdate = true
		updatedVehicle.InspectionBookExpiryDate = parseDate(vehicleDTO.InspectionBookExpiryDate)
		field := "Inspection book expiry date"
		logMapPrev[field] = vehicle.InspectionBookExpiryDate
		logMapNew[field] = updatedVehicle.InspectionBookExpiryDate
	}

	if vehicle.VehicleKM != vehicleDTO.VehicleKM {
		isAnyUpdate = true
		updatedVehicle.VehicleKM = vehicleDTO.VehicleKM
		field := "Vehicle KM"
		logMapPrev[field] = vehicle.VehicleKM
		logMapNew[field] = updatedVehicle.VehicleKM
	}

	if int(vehicle.NumberOfSpareTyres.Int64) != vehicleDTO.NumberOfSpareTyres {
		isAnyUpdate = true
		updatedVehicle.NumberOfSpareTyres = null.IntFrom(int64(vehicleDTO.NumberOfSpareTyres))
		field := "Number of spare tyres"
		logMapPrev[field] = vehicle.NumberOfSpareTyres
		logMapNew[field] = updatedVehicle.NumberOfSpareTyres
	}

	return updatedVehicle, isAnyUpdate, nil
}

func (uc *AssetVehicleUseCase) getRedirectLinkAssetVehicle(assetVehicle *models.AssetVehicle) string {
	apiURL := internalConstants.API_STAGING_URL
	if os.Getenv(internalConstants.ENV_APP_ENV) == "production" {
		apiURL = internalConstants.API_URL
	}
	return fmt.Sprintf("http://%s/v1/redirects?type=%s&ref=%s&client_id=%s", apiURL, notifConstants.REDIRECT_TYPE_ASSET_VEHICLE, assetVehicle.AssetID, assetVehicle.ClientID)
}

func (uc *AssetVehicleUseCase) notifyAfterUpdateAssetVehicle(
	ctx context.Context,
	assetID string,
) {
	assetAssignment, err := uc.assetAssignmentRepo.GetAssetAssignment(
		ctx, uc.DB.DB(), models.AssetAssignmentCondition{
			Where: models.AssetAssignmentWhere{
				AssetID:  assetID,
				Assigned: true,
			},
			Preload: models.AssetAssignmentPreload{
				Asset:      true,
				AssetBrand: true,
			},
		})
	if err != nil {
		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Warnf("failed to get asset assignment %v", err)
		}
		return
	}

	assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, uc.DB.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			AssetID: assetID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get tyre, err: %v", err)
		return
	}
	href := uc.getRedirectLinkAssetVehicle(assetVehicle)
	title, err := tmplhelpers.ParseStringTemplate(
		"Vehicle {{.vehicleName}} /. {{.serialNumber}} was updated",
		map[string]interface{}{
			"vehicleName":  assetAssignment.Asset.Name,
			"serialNumber": assetAssignment.Asset.SerialNumber,
		})
	if err != nil {
		commonlogger.Warnf("failed to create title %v", err)
		return
	}

	body, err := tmplhelpers.ParseStringTemplate(
		`Asset vehicle data was updated<br><br>
		<table>
		{{ range $key, $value := .Data }}
			<tr>
			<td>{{ $key }}</td>: <td>{{if $value }} {{ $value }} {{else}} - {{end}}</td>
			</tr>
		{{ end }}
		<br>
		<a href = "{{.RedirectLink}}"><button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">View Vehicle</button></a><br>
		</table>
		<br>`,
		struct {
			Data         map[string]interface{}
			RedirectLink template.URL
		}{
			RedirectLink: template.URL(href),
			Data: map[string]interface{}{
				"Status":                             assetAssignment.Asset.StatusLabel(),
				"Serial Number":                      assetAssignment.Asset.SerialNumber,
				"Brand":                              assetAssignment.Asset.Brand.BrandName,
				"Registration number":                assetVehicle.RegistrationNumber,
				"Number of tyres":                    assetVehicle.NumberOfTyres,
				"Engine model":                       assetVehicle.EngineModel,
				"Transmission model":                 assetVehicle.TransmissionModel,
				"Vrd number":                         assetVehicle.VrdNumber,
				"Vrd expiry date":                    assetVehicle.VrdExpiryDate,
				"Engine number":                      assetVehicle.EngineNumber,
				"Chassis number":                     assetVehicle.ChassisNumber,
				"Gps device imei":                    assetVehicle.GpsDeviceImei,
				"Registration certificate number":    assetVehicle.RegistrationCertificateNumber,
				"Registration certificate assign to": assetVehicle.RegistrationCertificateAssignTo,
				"Inspection book number":             assetVehicle.InspectionBookNumber,
				"Inspection book number assign to":   assetVehicle.InspectionBookNumberAssignTo,
				"Inspection book expiry date":        assetVehicle.InspectionBookExpiryDate,
			},
		},
	)
	if err != nil {
		commonlogger.Warnf("failed to body email %v", err)
		return
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            assetAssignment.UserID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_ASSET_VEHICLE,
		SourceReferenceID: assetAssignment.AssetID,
		TargetReferenceID: "",
		TargetURL:         "",
		MessageHeader:     title,
		MessageBody:       body,
		ClientID:          assetAssignment.ClientID,
		TypeCode:          "",
		ContentTypeCode:   "",
		ReferenceCode:     notifConstants.NOTIF_REF_ASSET,
		ReferenceValue:    assetAssignment.AssetID,
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: true,
	})

}

func (uc *AssetVehicleUseCase) GetVehicles(ctx context.Context, req dtos.VehicleListReq) (commonmodel.ListResponse, error) {
	resp := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return resp, err
	}

	totalRecords, vehicles, err := uc.AssetVehicleRepository.GetVehicles(ctx, uc.DB.DB(),
		models.GetVehicleListParam{
			ListRequest: req.ListRequest,
			Cond: models.VehicleCondition{
				Where: models.VehicleWhere{
					BrandID:            req.BrandID,
					BrandIDs:           req.BrandIDs,
					ClientIDAndGeneral: claim.GetLoggedInClientID(),
				},
			},
		},
	)
	if err != nil {
		return resp, err
	}

	if len(vehicles) == 0 {
		return resp, nil
	}

	respData := make([]dtos.VehicleListItemResp, 0, len(vehicles))
	for _, vehicle := range vehicles {
		respData = append(respData, dtos.VehicleListItemResp{
			ID:                vehicle.ID,
			VehicleTypeCode:   vehicle.VehicleTypeCode,
			Model:             vehicle.VehicleModel,
			EngineModel:       vehicle.EngineModel,
			TransmissionModel: vehicle.TransmissionModel,
			IsGeneral:         vehicle.IsGeneral(),
			VehicleNumber:     vehicle.VehicleNumber,
			AxleConfiguration: vehicle.AxleConfiguration,
			BrandID:           vehicle.BrandID,
			Brand: dtos.Brand{
				ID:        vehicle.Brand.ID,
				BrandName: vehicle.Brand.BrandName,
			},
		})
	}

	resp = commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}

	return resp, nil
}

func (uc *AssetVehicleUseCase) CreateVehicle(ctx context.Context, req dtos.CreateVehicleReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	vehicle := &models.Vehicle{
		BrandID:           req.BrandID,
		VehicleTypeCode:   req.VehicleTypeCode,
		VehicleModel:      req.Model,
		EngineModel:       req.EngineModel,
		TransmissionModel: req.TransmissionModel,
		AxleConfiguration: req.AxleConfiguration,
		ClientID:          claim.GetLoggedInClientID(),
	}
	err = uc.AssetVehicleRepository.CreateVehicle(ctx, uc.DB.DB(), vehicle)
	if err != nil {
		return nil, err
	}

	resp := dtos.VehicleResp{
		ID:                vehicle.ID,
		BrandID:           vehicle.BrandID,
		VehicleTypeCode:   vehicle.VehicleTypeCode,
		Model:             vehicle.VehicleModel,
		EngineModel:       vehicle.EngineModel,
		TransmissionModel: vehicle.TransmissionModel,
		IsGeneral:         vehicle.IsGeneral(),
		VehicleNumber:     vehicle.VehicleNumber,
		AxleConfiguration: vehicle.AxleConfiguration,
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: vehicle.ID,
		Data:        resp,
	}, nil
}

func isEqualArray(a, b []string) bool {

	counts := make(map[string]int)

	for _, val := range a {
		counts[val]++
	}

	for _, val := range b {
		counts[val]--
		if counts[val] < 0 {
			return false
		}
	}

	for _, count := range counts {
		if count != 0 {
			return false
		}
	}

	return true
}

func (uc *AssetVehicleUseCase) checkUpdateVehicleAxleConfig(ctx context.Context, before *models.Vehicle, req []string) (bool, error) {
	beforeAxleConfiguration := models.AxleConfigurationNewToOld(before.AxleConfiguration)
	if len(req) != len(beforeAxleConfiguration) {
		return true, nil
	}
	isUpdate := !isEqualArray(req, beforeAxleConfiguration)
	return isUpdate, nil
}

func (uc *AssetVehicleUseCase) UpdateVehicle(ctx context.Context, id string, req dtos.UpdateVehicleReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	vehicle, err := uc.AssetVehicleRepository.GetVehicle(ctx, uc.DB.DB(), models.VehicleCondition{
		Where: models.VehicleWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}
	isUpdateAxle, err := uc.checkUpdateVehicleAxleConfig(ctx, vehicle, models.AxleConfigurationNewToOld(req.AxleConfiguration))
	if err != nil {
		return nil, err
	}

	isAnyUpdate, updateVehicle := checkUpdateVehicle(vehicle, req)

	if isUpdateAxle {
		updateVehicle.AxleConfiguration = req.AxleConfiguration
	}

	if !isAnyUpdate && !isUpdateAxle {
		return &commonmodel.UpdateResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: vehicle.ID,
			Data:        nil,
		}, nil
	}

	err = uc.AssetVehicleRepository.UpdateVehicle(ctx, uc.DB.WithCtx(ctx).DB(), updateVehicle)
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: vehicle.ID,
		Data:        nil,
	}, nil
}

func checkUpdateVehicle(before *models.Vehicle, req dtos.UpdateVehicleReq) (bool, *models.Vehicle) {
	updateVehicle := &models.Vehicle{}
	updateVehicle.ID = before.ID
	isAnyUpdate := false

	if req.BrandID != "" && req.BrandID != before.BrandID {
		isAnyUpdate = true
		updateVehicle.BrandID = req.BrandID
	}

	if req.EngineModel != "" && req.EngineModel != before.EngineModel {
		isAnyUpdate = true
		updateVehicle.EngineModel = req.EngineModel
	}

	if req.Model != "" && req.Model != before.VehicleModel {
		isAnyUpdate = true
		updateVehicle.VehicleModel = req.Model
	}

	if req.TransmissionModel != "" && req.TransmissionModel != before.TransmissionModel {
		isAnyUpdate = true
		updateVehicle.TransmissionModel = req.TransmissionModel
	}

	if req.VehicleTypeCode != "" && req.VehicleTypeCode != before.VehicleTypeCode {
		isAnyUpdate = true
		updateVehicle.VehicleTypeCode = req.VehicleTypeCode
	}

	return isAnyUpdate, updateVehicle
}

func (uc *AssetVehicleUseCase) GetVehicle(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	vehicle, err := uc.AssetVehicleRepository.GetVehicle(ctx, uc.DB.DB(), models.VehicleCondition{
		Where: models.VehicleWhere{
			ID:                 id,
			ClientIDAndGeneral: claim.GetLoggedInClientID(),
		},
		Preload: models.VehiclePreload{
			Brand: true,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := dtos.VehicleDetailResp{
		ID:                id,
		BrandID:           vehicle.BrandID,
		BrandName:         vehicle.Brand.BrandName,
		VehicleTypeCode:   vehicle.VehicleTypeCode,
		Model:             vehicle.VehicleModel,
		EngineModel:       vehicle.EngineModel,
		TransmissionModel: vehicle.TransmissionModel,
		IsGeneral:         vehicle.IsGeneral(),
		VehicleNumber:     vehicle.VehicleNumber,
		AxleConfiguration: vehicle.AxleConfiguration,
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        resp,
	}, nil
}

func (uc *AssetVehicleUseCase) DeleteVehicle(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.AssetVehicleRepository.GetVehicle(ctx, uc.DB.DB(), models.VehicleCondition{
		Where: models.VehicleWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.AssetVehicleRepository.DeleteVehicleByID(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *AssetVehicleUseCase) UpdateVehicleKM(ctx context.Context, tX database.DBUsecase, assetVehicle *models.AssetVehicle, vehicleKM float64) error {
	if vehicleKM == 0 {
		return nil
	}

	err := uc.AssetVehicleRepository.UpdateAssetVehicleKMV2(ctx, tX.DB(), assetVehicle.AssetID, vehicleKM)
	if err != nil {
		return err
	}

	assetLinkedAssetVehicleTyres, err := uc.assetLinkedRepo.GetAssetLinkedTyresV2(ctx, tX.DB(), models.AssetLinkedVehicleTyreCondition{
		Where: models.AssetLinkedVehicleTyreWhere{
			ParentAssetID: assetVehicle.AssetID,
		},
		Preload: models.AssetLinkedVehicleTyrePreload{
			AssetLinked: true,
		},
	})
	if err != nil {
		return err
	}

	var increasedKM float64 = vehicleKM - assetVehicle.VehicleKM
	updateAssetTyreIDs := []string{}
	for _, assetLinkedAssetVehicleTyre := range assetLinkedAssetVehicleTyres {
		if assetLinkedAssetVehicleTyre.TyrePosition <= int(assetVehicle.NumberOfTyres.Int64) {
			updateAssetTyreIDs = append(updateAssetTyreIDs, assetLinkedAssetVehicleTyre.AssetLinked.ChildAssetID)
		}
	}

	if len(updateAssetTyreIDs) == 0 {
		return nil
	}

	err = uc.assetTyreRepo.IncreaseAssetTyreKM(ctx, tX.DB(), updateAssetTyreIDs, int(increasedKM))
	if err != nil {
		return err
	}

	return nil
}

func (uc *AssetVehicleUseCase) UpdateAssetVehicleKm(ctx context.Context, id string, vehicleKM int) (*commonmodel.UpdateResponse, error) {
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, tX.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			AssetID: id,
		},
	})
	if err != nil {
		return nil, err
	}

	if assetVehicle.VehicleKM > float64(vehicleKM) {
		return nil, errorhandler.ErrBadRequest(
			errorconstants.ERR_MSG_VEHILCE_KM_MUST_NOT_LESS_THAN_BEFORE,
		)
	}

	err = uc.UpdateVehicleKM(ctx, tX, assetVehicle, float64(vehicleKM))
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *AssetVehicleUseCase) UpdateAssetVehicleMeter(ctx context.Context, req dtos.UpdateVehicleMeterReq) (*commonmodel.UpdateResponse, error) {
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, tX.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			AssetID: req.AssetID,
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.updateAssetVehicleMeter(ctx, tX, assetVehicle, req)
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: req.AssetID,
		Data:        nil,
	}, nil
}

func (uc *AssetVehicleUseCase) updateAssetVehicleMeter(ctx context.Context, tX database.DBUsecase, assetVehicle *models.AssetVehicle, req dtos.UpdateVehicleMeterReq) error {
	if assetVehicle.UseKilometer.Bool {
		if assetVehicle.VehicleKM > float64(req.VehicleKM) {
			return errorhandler.ErrBadRequest(
				errorconstants.ERR_MSG_VEHILCE_KM_MUST_NOT_LESS_THAN_BEFORE,
			)
		}
		err := uc.AssetVehicleRepository.UpdateAssetVehicleKMV2(ctx, tX.DB(), assetVehicle.AssetID, float64(req.VehicleKM))
		if err != nil {
			return err
		}
	}

	if assetVehicle.UseHourmeter.Bool {
		if assetVehicle.VehicleHm > calculationhelpers.Multiply100(req.VehicleHm) {
			return errorhandler.ErrBadRequest(
				errorconstants.ERR_MSG_VEHILCE_HM_MUST_NOT_LESS_THAN_BEFORE,
			)
		}
		err := uc.AssetVehicleRepository.UpdateAssetVehicleHm(ctx, tX.DB(), assetVehicle.AssetID, calculationhelpers.Multiply100(req.VehicleHm))
		if err != nil {
			return err
		}
	}

	assetLinkedAssetVehicleTyres, err := uc.assetLinkedRepo.GetAssetLinkedTyresV2(ctx, tX.DB(), models.AssetLinkedVehicleTyreCondition{
		Where: models.AssetLinkedVehicleTyreWhere{
			ParentAssetID: assetVehicle.AssetID,
		},
		Preload: models.AssetLinkedVehicleTyrePreload{
			AssetLinked: true,
		},
	})
	if err != nil {
		return err
	}

	updateAssetTyreIDs := []string{}
	for _, assetLinkedAssetVehicleTyre := range assetLinkedAssetVehicleTyres {
		if assetLinkedAssetVehicleTyre.TyrePosition <= int(assetVehicle.NumberOfTyres.Int64) {
			updateAssetTyreIDs = append(updateAssetTyreIDs, assetLinkedAssetVehicleTyre.AssetLinked.ChildAssetID)
		}
	}

	if len(updateAssetTyreIDs) == 0 {
		return nil
	}

	if assetVehicle.UseKilometer.Bool && len(updateAssetTyreIDs) > 0 {
		increasedKM := req.VehicleKM - int(assetVehicle.VehicleKM)
		err = uc.assetTyreRepo.IncreaseAssetTyreKM(ctx, tX.DB(), updateAssetTyreIDs, int(increasedKM))
		if err != nil {
			return err
		}

	}

	if assetVehicle.UseHourmeter.Bool && len(updateAssetTyreIDs) > 0 {
		increasedKM := calculationhelpers.Multiply100(req.VehicleHm) - assetVehicle.VehicleHm
		err = uc.assetTyreRepo.IncreaseAssetTyreHm(ctx, tX.DB(), updateAssetTyreIDs, increasedKM)
		if err != nil {
			return err
		}
	}

	return nil
}

func generateAssetVehicleBulkUploadFilePath(clientID, name string) string {
	return clientID + "/ASSET_VEHICLE/BULK_UPLOAD/" + helpers.GenerateSecureFileName(name)
}

func (uc *AssetVehicleUseCase) ParseAssetVehicleBulkUpload(ctx context.Context, req commonmodel.BulkUploadReq) ([]dtos.BulkUploadAssetVehicleReq, []byte, error) {
	file, err := req.FileHeader.Open()
	if err != nil {
		return nil, nil, err
	}
	defer file.Close()

	bufReader := bufio.NewReader(file)
	bytes, err := io.ReadAll(bufReader)
	if err != nil {
		return nil, nil, err
	}

	bytesCopy := make([]byte, len(bytes))
	copy(bytesCopy, bytes)
	data := []dtos.BulkUploadAssetVehicleReq{}
	if err := gocsv.UnmarshalBytes(bytesCopy, &data); err != nil {
		return nil, nil, err
	}

	if len(data) > 50 {
		return nil, nil, errorhandler.ErrBadRequest(errorhandler.ErrExceedMaxRow)
	}

	return data, bytes, nil
}

func (uc *AssetVehicleUseCase) ValidateAssetVehicleBulkUpload(ctx context.Context, req []dtos.BulkUploadAssetVehicleReq) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}
	clientID := claim.GetLoggedInClientID()

	userEmails := []string{}
	bodyTypeNumbers := []string{}
	brandNumbers := []string{}
	engineNumbers := []string{}
	regisNumbers := []string{}
	for _, r := range req {
		userEmails = append(userEmails, r.RegistrationCertificateAssignToEmail)
		userEmails = append(userEmails, r.InspectionBookNumberAssignToEmail)
		userEmails = append(userEmails, r.AssingedToUserEmail)

		bodyTypeNumbers = append(bodyTypeNumbers, r.AssetVehicleBodyTypeNo)
		brandNumbers = append(brandNumbers, r.BrandNo)
		engineNumbers = append(engineNumbers, r.EngineNumber)
		regisNumbers = append(regisNumbers, r.RegistrationNumber)
	}

	users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			Emails:   userEmails,
			ClientID: clientID,
		},
	})
	if err != nil {
		return err
	}
	mapEmailUserToID := map[string]string{}
	for i := range users {
		mapEmailUserToID[users[i].Email] = users[i].ID
	}

	bodyTypes, err := uc.vehicleBodyTypeRepo.GetAssetVehicleBodyTypes(ctx, uc.DB.DB(), models.AssetVehicleBodyTypeCondition{
		Where: models.AssetVehicleBodyTypeWhere{
			ClientID:               clientID,
			VehicleBodyTypeNumbers: bodyTypeNumbers,
		},
	})
	if err != nil {
		return err
	}
	mapBodyTypes := map[string]string{}
	for _, bodyType := range bodyTypes {
		mapBodyTypes[bodyType.VehicleBodyTypeNumber] = bodyType.ID
	}

	brands, err := uc.brandRepo.GetBrands(ctx, uc.DB.DB(), models.BrandCondition{
		Where: models.BrandWhere{
			ClientID:     clientID,
			BrandNumbers: brandNumbers,
			Tags:         []string{constants.BRAND_TAG_VEHICLE},
		},
	})
	if err != nil {
		return err
	}
	mapBrands := map[string]string{}
	for _, brand := range brands {
		mapBrands[brand.BrandNumber] = brand.ID
	}

	assetVehiclesEngineNumber, err := uc.AssetVehicleRepository.GetAssetVehiclesV2(ctx, uc.DB.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			EngineNumbers: engineNumbers,
		},
		Preload: models.AssetVehiclePreload{},
		Columns: []string{"engine_number"},
	})
	if err != nil {
		return err
	}

	mapEngineNumbers := map[string]bool{}
	for _, assetVehicle := range assetVehiclesEngineNumber {
		mapEngineNumbers[assetVehicle.EngineNumber] = true
	}

	assetVehiclesRegisNumber, err := uc.AssetVehicleRepository.GetAssetVehiclesV2(ctx, uc.DB.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			RegistrationNumbers: regisNumbers,
		},
		Preload: models.AssetVehiclePreload{},
		Columns: []string{"registration_number"},
	})
	if err != nil {
		return err
	}

	mapRegistNumbers := map[string]bool{}
	for _, assetVehicle := range assetVehiclesRegisNumber {
		mapRegistNumbers[assetVehicle.EngineNumber] = true
	}

	for i := range req {
		req[i].IsValidToProcess = true
		req[i].ValidateUseKilometerHourmeter()
		req[i].ValidateRegistrationCertificateAssignToEmail(mapEmailUserToID)
		req[i].ValidateInspectionBookNumberAssignToEmail(mapEmailUserToID)
		req[i].ValidateAssingedToUserEmail(mapEmailUserToID)
		req[i].ValidateAssetVehicleBodyTypeNo(mapBodyTypes)
		req[i].ValidateBrandNo(mapBrands)
		req[i].ValidateEngineNumber(mapEngineNumbers)
		req[i].ValidateRegistrationNumber(mapRegistNumbers)
	}

	return nil
}

func (uc *AssetVehicleUseCase) AssetVehicleBulkUpload(ctx context.Context, fileName string, oriFileByte []byte, req []dtos.BulkUploadAssetVehicleReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	numErrorRows := 0
	for i := 0; i < len(req); i++ {
		if req[i].IsSuccess {
			continue
		}

		if !req[i].IsValidToProcess {
			numErrorRows++
			continue
		}

		asset, err := uc.CreateAssetVehicle(ctx, &dtos.AssetVehicleListReceiver{
			RegistrationNumber:              req[i].RegistrationNumber,
			AssetVehicleBodyTypeCode:        req[i].AssetVehicleBodyTypeCode,
			NumberOfTyres:                   null.IntFrom(int64(req[i].NumberOfTyres)),
			EngineModel:                     req[i].EngineModel,
			TransmissionModel:               req[i].TransmissionModel,
			VrdNumber:                       req[i].VrdNumber,
			VrdExpiryDate:                   req[i].VrdExpiryDate,
			EngineNumber:                    req[i].EngineNumber,
			ChassisNumber:                   req[i].ChassisNumber,
			GpsDeviceImei:                   req[i].GpsDeviceImei,
			RegistrationCertificateNumber:   req[i].RegistrationCertificateNumber,
			RegistrationCertificateAssignTo: req[i].RegistrationCertificateAssignToUserID,
			InspectionBookNumber:            req[i].InspectionBookNumber,
			InspectionBookExpiryDate:        req[i].InspectionBookExpiryDate,
			InspectionBookNumberAssignTo:    req[i].InspectionBookNumberAssignToUserID,
			BrandID:                         req[i].BrandID,
			VehicleName:                     req[i].Name,
			ModelNumber:                     req[i].ModelNumber,
			SerialNumber:                    req[i].SerialNumber,
			ProductionYear:                  req[i].ProductionYear,
			AssingedToUserID:                req[i].AssingedToUserID,
			Cost:                            req[i].Cost,
			StatusCode:                      constants.ASSET_STATUS_CODE_NEW_STOCK,
			VehicleKM:                       req[i].VehicleKM,
			VehicleHm:                       req[i].VehicleHm,
			NumberOfSpareTyres:              req[i].NumberOfSpareTyres,
			ClientID:                        claim.GetLoggedInClientID(),
			UseKilometer:                    req[i].UseKilometer,
			UseHourmeter:                    req[i].UseHourmeter,
		})
		if err != nil {
			commonlogger.Warnf("failed to process bulk upload asset vehicle", i, err)
			req[i].FailedReason = err.Error()
			numErrorRows++
			continue
		}

		req[i].ReferenceID = asset.ID
		req[i].IsSuccess = true
	}

	uploadStatus := uploadConstants.USER_UPLOAD_STATUS_SUCCESS_CODE
	if numErrorRows > 0 {
		if numErrorRows == len(req) {
			uploadStatus = uploadConstants.USER_UPLOAD_STATUS_FAILED_CODE
		} else {
			uploadStatus = uploadConstants.USER_UPLOAD_STATUS_PARTIAL_SUCCESS_CODE
		}
	}

	resultBytes, err := gocsv.MarshalBytes(&req)
	if err != nil {
		return nil, err
	}

	oriFilePath := generateAssetVehicleBulkUploadFilePath(claim.GetLoggedInClientID(), fileName)
	err = uc.storageUseCase.UploadCsvFile(ctx, oriFilePath, oriFileByte)
	if err != nil {
		return nil, err
	}

	resultFilePath := generateAssetVehicleBulkUploadFilePath(claim.GetLoggedInClientID(), "result_"+fileName)
	err = uc.storageUseCase.UploadCsvFile(ctx, resultFilePath, resultBytes)
	if err != nil {
		return nil, err
	}

	userBulkUpload := &uploadModel.UserBulkUpload{
		ModelV2:            commonmodel.ModelV2{},
		BulkUploadCode:     uploadConstants.BULK_UPLOAD_ASSET_VEHICLE_CODE,
		StatusCode:         uploadStatus,
		OriginalFilePath:   oriFilePath,
		ResultFilePath:     resultFilePath,
		NumberOfSuccessRow: len(req) - numErrorRows,
		NumberOfFailedRow:  numErrorRows,
	}

	err = uc.uploadRepo.CreateUserBulkUploadData(ctx, uc.DB.WithCtx(ctx).DB(), userBulkUpload)
	if err != nil {
		return nil, err
	}

	resp := uploadDto.UserBulkUploadResp{
		ID:                 userBulkUpload.ID,
		BulkUploadCode:     userBulkUpload.BulkUploadCode,
		StatusCode:         userBulkUpload.StatusCode,
		OriginalFilePath:   userBulkUpload.OriginalFilePath,
		ResultFilePath:     userBulkUpload.ResultFilePath,
		NumberOfSuccessRow: userBulkUpload.NumberOfSuccessRow,
		NumberOfFailedRow:  userBulkUpload.NumberOfFailedRow,
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: userBulkUpload.ID,
		Data:        resp,
	}, nil
}

func (uc *AssetVehicleUseCase) GetVehiclesCSV(ctx context.Context) ([]dtos.VehicleCSV, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	assetVehicleCSV := []dtos.VehicleCSV{}

	assetVehicles, err := uc.AssetVehicleRepository.GetVehiclesCSV(ctx, uc.DB.DB(), models.VehicleCondition{
		Where: models.VehicleWhere{
			ClientID: claim.GetLoggedInClientID(),
		},
	})

	if err != nil {
		return nil, err
	}
	for _, v := range assetVehicles {
		columnData := dtos.VehicleCSV{
			VehicleNo:         v.VehicleNumber,
			VehicleType:       v.VehicleTypeCode,
			BrandName:         v.Brand.BrandName,
			Model:             v.VehicleModel,
			EngineModel:       v.EngineModel,
			TransmissionModel: v.TransmissionModel,
		}
		assetVehicleCSV = append(assetVehicleCSV, columnData)
	}
	return assetVehicleCSV, nil
}

func (uc *AssetVehicleUseCase) UpdateAssetVehicleAxleConfiguration(ctx context.Context, assetId string, req dtos.UpdateAssetVehicleAxleConfigurationReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.AssetVehicleRepository.GetAssetVehicle(
		ctx,
		uc.DB.DB(),
		models.AssetVehicleCondition{
			Where: models.AssetVehicleWhere{
				AssetID:  assetId,
				ClientID: claim.GetLoggedInClientID(),
			},
		},
	)
	if err != nil {
		return nil, err
	}

	totalTyres, totalSpareTyres := utils.CountAxleConfigurationTyres(models.AxleConfigurationNewToOld(req.AxleConfiguration))

	err = uc.AssetVehicleRepository.UpdateAssetVehicle(
		ctx,
		uc.DB.WithCtx(ctx).DB(),

		&models.AssetVehicle{
			AssetID:             assetId,
			AxleConfiguration:   req.AxleConfiguration,
			MaxRtdDiffTolerance: &req.MaxRtdDiffTolerance,
			NumberOfTyres:       null.IntFrom(int64(totalTyres)),
			NumberOfSpareTyres:  null.IntFrom(int64(totalSpareTyres)),
		},
	)
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetId,
		Data:        nil,
	}, nil
}

func (uc *AssetVehicleUseCase) PopulatePeriodicAssetVehicleStatsHistories(ctx context.Context) (*commonmodel.CreateResponse, error) {
	err := uc.AssetVehicleRepository.PopulatePeriodicAssetVehicleStatsHistory(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	err = uc.FinanceRepository.PopulatePeriodicJournalRefSumAmountHistory(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *AssetVehicleUseCase) GetLastMonthAssetVehicleStatsHistory(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	now := time.Now()

	// Get the first day of the current month
	firstDayOfCurrentMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// Subtract one day to get the last day of the previous month
	lastDayOfLastMonth := firstDayOfCurrentMonth.AddDate(0, 0, -1)

	assetVehicleStatsHistory, err := uc.AssetVehicleRepository.GetAssetVehicleStatsHistory(ctx, uc.DB.DB(), lastDayOfLastMonth, assetID, claim.GetLoggedInClientID())
	if err != nil {
		if errorhandler.IsErrNotFound(err) {
			return &commonmodel.DetailResponse{
				Success:     true,
				Message:     "Success",
				ReferenceID: "",
				Data:        nil,
			}, nil
		}
		return nil, err
	}

	var vehicleHM *float64
	if assetVehicleStatsHistory.VehicleHM != nil {
		temp := calculationhelpers.Div100(*assetVehicleStatsHistory.VehicleHM)
		vehicleHM = &temp
	}

	resp := dtos.AssetVehicleStatsHistoryResp{
		Datetime:       lastDayOfLastMonth,
		AssetID:        assetID,
		VehicleKM:      assetVehicleStatsHistory.VehicleKM,
		VehicleHM:      vehicleHM,
		TotalAssetCost: nil,
	}

	journalRefSumAmountHistory, err := uc.FinanceRepository.GetJournalRefSumAmountHistory(ctx, uc.DB.DB(), lastDayOfLastMonth, assetID, "ASSET", claim.GetLoggedInClientID())
	if err != nil {
		if errorhandler.IsErrNotFound(err) {
			return &commonmodel.DetailResponse{
				Success:     true,
				Message:     "Success",
				ReferenceID: "",
				Data:        resp,
			}, nil
		}
		return nil, err
	}

	resp.TotalAssetCost = &journalRefSumAmountHistory.Amount

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        resp,
	}, nil
}
