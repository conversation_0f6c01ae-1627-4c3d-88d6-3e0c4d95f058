package usecase

import (
	approvalConstants "assetfindr/internal/app/approval/constants"
	approvalModels "assetfindr/internal/app/approval/models"
	approvalUsecase "assetfindr/internal/app/approval/usecase"
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/app/asset/utils"
	contentConstants "assetfindr/internal/app/content/constants"
	contentUsecase "assetfindr/internal/app/content/usecase"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	userConstants "assetfindr/internal/app/user-identity/constants"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/sqlhelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"context"
	"database/sql"
	"errors"
	"fmt"
	"html/template"
	"strings"
	"time"

	integrationModels "assetfindr/internal/app/integration/models"
	integrationRepository "assetfindr/internal/app/integration/repository"

	userIdentityRepository "assetfindr/internal/app/user-identity/repository"

	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type AssetUseCase struct {
	DB                        database.DBUsecase
	AssetRepository           repository.AssetRepository
	AssetVehicleRepository    repository.AssetVehicleRepository
	AssetTryeRepository       repository.AssetTyreRepository
	AssetAssignmentUseCase    *AssetAssignmentUseCase
	UserRepository            userIdentityRepository.UserRepository
	AssetLogRepo              repository.AssetLogRepository
	AssetAssignmentRepository repository.AssetAssignmentRepository
	notifUseCase              *notificationUsecase.NotificationUseCase
	storageUsecase            *storageUsecase.AttachmentUseCase
	formUsecase               contentUsecase.FormUseCase
	ApprovalUseCase           *approvalUsecase.ApprovalUseCase
	AssetVehicleUseCase       *AssetVehicleUseCase
	AlertRepository           integrationRepository.AlertRepository
	AssetLinkedRepo           repository.AssetLinkedRepository
	IntegrationRepo           integrationRepository.IntegrationRepository
	AssetTyreRepo             repository.AssetTyreRepository
	assetInspectionTypeRepo   repository.AssetInspectionTyreRepository
}

func NewAssetUseCase(
	DB database.DBUsecase,
	assetRepo repository.AssetRepository,
	userRepo userIdentityRepository.UserRepository,
	assetVehicleRepository repository.AssetVehicleRepository,
	assetLogRepo repository.AssetLogRepository,
	assetTryeRepository repository.AssetTyreRepository,
	assetAssignmentRepo repository.AssetAssignmentRepository,
	storageUsecase *storageUsecase.AttachmentUseCase,
	formUsecase contentUsecase.FormUseCase,
	approvalUseCase *approvalUsecase.ApprovalUseCase,
	alertRepository integrationRepository.AlertRepository,
	assetLinkedRepo repository.AssetLinkedRepository,
	integrationRepo integrationRepository.IntegrationRepository,
	AssetTyreRepo repository.AssetTyreRepository,
	assetInspectionTypeRepo repository.AssetInspectionTyreRepository,
) *AssetUseCase {
	return &AssetUseCase{
		DB:                        DB,
		AssetRepository:           assetRepo,
		AssetVehicleRepository:    assetVehicleRepository,
		AssetTryeRepository:       assetTryeRepository,
		AssetAssignmentUseCase:    nil,
		UserRepository:            userRepo,
		AssetLogRepo:              assetLogRepo,
		AssetAssignmentRepository: assetAssignmentRepo,
		storageUsecase:            storageUsecase,
		formUsecase:               formUsecase,
		ApprovalUseCase:           approvalUseCase,
		AlertRepository:           alertRepository,
		AssetLinkedRepo:           assetLinkedRepo,
		IntegrationRepo:           integrationRepo,
		AssetTyreRepo:             assetTryeRepository,
		assetInspectionTypeRepo:   assetInspectionTypeRepo,
	}
}

func (uc *AssetUseCase) SetNotifUseCase(notifUsecase notificationUsecase.NotificationUseCase) {
	uc.notifUseCase = &notifUsecase
}

func (uc *AssetUseCase) UpdateAssetAssignmentUseCase(assetAssignmentUseCase *AssetAssignmentUseCase) {
	uc.AssetAssignmentUseCase = assetAssignmentUseCase
}

func (uc *AssetUseCase) UpdateAssetVehicleUseCase(assetVehicleUseCase *AssetVehicleUseCase) {
	uc.AssetVehicleUseCase = assetVehicleUseCase
}

func (uc *AssetUseCase) constructAssetModel(req dtos.CreateUpdateAsset) (*models.Asset, error) {
	modelID := null.NewString(req.ModelID, req.ModelID != "")
	return &models.Asset{
		AssetCategoryCode:        req.CategoryCode,
		SubCategoryCode:          req.SubCategoryCode,
		CustomAssetCategoryID:    req.CustomCategoryID,
		CustomAssetSubCategoryID: &req.CustomSubCategoryID,
		Name:                     req.Name,
		BrandID:                  req.BrandID,
		SerialNumber:             req.SerialNumber,
		OwnershipCategoryCode:    req.OwnershipCategoryCode,
		AssetStatusCode:          req.StatusCode,
		LocationID:               req.LocationID,
		ReferenceNumber:          req.ReferenceNumber,
		PartnerOwnerID:           req.PartnerOwnerID,
		PartnerOwnerNo:           req.PartnerOwnerNo,
		PartnerOwnerName:         req.PartnerOwnerName,
		Rfid:                     req.Rfid,
		GpsImei:                  req.GpsImei,
		ModelID:                  &modelID,
		Address:                  req.Address,
		AddressMapLink:           req.AddressMapLink,
		UsingPartnerOwnerAddress: req.UsingPartnerOwnerAddress,
	}, nil
}

func (uc *AssetUseCase) constructAssetAssignmet(assetID string, userID string) (*models.AssetAssignment, error) {
	assetAssignment := models.AssetAssignment{}
	assetAssignment.AssetID = assetID
	assetAssignment.UserID = userID
	assetAssignment.AssignedDateTime = time.Now()
	return &assetAssignment, nil
}

func (uc *AssetUseCase) constructUserIds(ctx context.Context, req dtos.CreateUpdateAsset) ([]string, error) {
	userIds := []string{}
	for _, val := range req.AssignedTo {
		userIds = append(userIds, val.UserID)
	}

	return userIds, nil
}

func (uc *AssetUseCase) constructAsssetAssignmentGroups(assetID string, userIds []string) ([]models.AssetAssignmentGroup, error) {
	assetAssignmentGroups := make([]models.AssetAssignmentGroup, 0)
	for _, val := range userIds {
		assetAssignmentGroup := models.AssetAssignmentGroup{}
		assetAssignmentGroup.AssetID = assetID
		assetAssignmentGroup.UserID = val
		assetAssignmentGroups = append(assetAssignmentGroups, assetAssignmentGroup)
	}
	return assetAssignmentGroups, nil
}

func (uc *AssetUseCase) UpdateAssetManagement(ctx context.Context, id string, req dtos.CreateUpdateAsset) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()
	oldAssetAssignmentGroup, err := uc.AssetAssignmentRepository.GetAssetAssignmentGroup(ctx, tx.DB(), models.AssetAssignmentCondition{
		Where: models.AssetAssignmentWhere{
			AssetID:  id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}
	asset, err := uc.constructAssetModel(req)
	if err != nil {
		return nil, err
	}

	currentAsset, err := uc.AssetRepository.GetAsset(ctx, tx.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.AssetPreload{
			AssetAssignment: true,
		}})
	if err != nil {
		return nil, err
	}
	// case delete
	if req.Photo == "" {
		asset.Photo = null.StringFrom("")

	} else if commonmodel.IsInTempFileLoc(req.Photo) {
		// case update
		destPhoto, err := uc.storageUsecase.MoveClientPhotoStorage(ctx, id, req.Photo)
		if err != nil {
			return nil, err
		}
		asset.Photo = null.StringFrom(destPhoto)
	}

	brandID := null.NewString(req.BrandID, req.BrandID != "")
	serialNumber := null.StringFrom(req.SerialNumber)
	referenceNumber := null.StringFrom(req.ReferenceNumber)
	rfid := null.StringFrom(req.Rfid)

	err = uc.AssetRepository.UpdateAssetForUpdateWithNull(ctx, tx.DB(), id, &models.AssetForUpdateWithNull{
		Asset:           *asset,
		BrandID:         &brandID,
		SerialNumber:    serialNumber,
		ReferenceNumber: referenceNumber,
		Rfid:            rfid,
	})
	if err != nil {
		return nil, err
	}
	userIds, err := uc.constructUserIds(ctx, req)
	if err != nil {
		return nil, err
	}
	newAssetAssignmentGroup, err := uc.constructAsssetAssignmentGroups(id, userIds)
	if err != nil {
		return nil, err
	}

	newAssignee := []string{}
	switch {
	case len(newAssetAssignmentGroup) > len(oldAssetAssignmentGroup):
		for pos, val := range oldAssetAssignmentGroup {
			err = uc.AssetAssignmentRepository.UpdateAssetAssignmentGroup(ctx, tx.DB(), val.ID, &newAssetAssignmentGroup[pos])
			if err != nil {
				return nil, err
			}
		}

		err = uc.AssetAssignmentRepository.CreateAssetAssignmentGroup(ctx, tx.DB(), newAssetAssignmentGroup[len(oldAssetAssignmentGroup):])
		if err != nil {
			return nil, err
		}

		for _, x := range newAssetAssignmentGroup[len(oldAssetAssignmentGroup):] {
			newAssignee = append(newAssignee, x.UserID)
		}

	case len(newAssetAssignmentGroup) < len(oldAssetAssignmentGroup):
		for pos, val := range newAssetAssignmentGroup {
			err = uc.AssetAssignmentRepository.UpdateAssetAssignmentGroup(ctx, tx.DB(), oldAssetAssignmentGroup[pos].ID, &val)
			if err != nil {
				return nil, err
			}
		}

		err := uc.AssetAssignmentRepository.DeleteAssetAssignmentGroup(ctx, tx.DB(), oldAssetAssignmentGroup[len(newAssetAssignmentGroup):])
		if err != nil {
			return nil, err
		}
	default:
		for pos, val := range oldAssetAssignmentGroup {
			err = uc.AssetAssignmentRepository.UpdateAssetAssignmentGroup(ctx, tx.DB(), val.ID, &newAssetAssignmentGroup[pos])
			if err != nil {
				return nil, err
			}
		}

	}

	// Assign or Reassign asset assignment
	if req.AssetAssignmentUserID != "" {
		if currentAsset.AssetAssignment.UserID != req.AssetAssignmentUserID {
			if currentAsset.AssetAssignment.UserID == "" {
				assetAssignment, err := uc.constructAssetAssignmet(asset.ID, req.AssetAssignmentUserID)
				if err != nil {
					return nil, err
				}
				assetAssignment.ClientID = claim.GetLoggedInClientID()
				err = uc.AssetAssignmentRepository.CreateAssetAssignment(ctx, tx.DB(), assetAssignment)
				if err != nil {
					return nil, err
				}
			} else {
				err = uc.AssetAssignmentUseCase.ReassignAssetAssignmentV2(ctx, tx, id, req.AssetAssignmentUserID, claim.UserID)
				if err != nil {
					return nil, err
				}
			}
		}
	}

	_, err = uc.storageUsecase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET,
		SourceReferenceID: asset.ID,
		TargetReferenceID: "",
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	if len(req.FormFields) > 0 {
		_, err = uc.formUsecase.UpsertForm(ctx, commonmodel.FormReq{
			TemplateID:       req.TemplateID,
			ReferenceID:      id,
			FormCategoryCode: contentConstants.FORM_CATEGORY_ASSET_ADDITIONAL_CODE,
			Fields:           req.FormFields,
		})
		if err != nil {
			return nil, err
		}
	}

	if req.CustomCategoryID != "" && req.BrandID != "" {
		err = uc.AssetVehicleUseCase.brandRepo.InsertBrandCustomCategoryMappingIgnoreConflict(ctx, tx.DB(), models.BrandCustomCategoryMapping{
			BrandID:               req.BrandID,
			CustomAssetCategoryID: req.CustomCategoryID,
			StatusCode:            constants.BRAND_CUSTOM_CATEGORY_MAPPING_STATUS_ACTIVE,
		})

		if err != nil {
			return nil, err
		}
	}

	if req.AssetVehicle != nil {
		assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, tx.DB(), models.AssetVehicleCondition{
			Where: models.AssetVehicleWhere{
				AssetID: id,
			},
		})
		if err != nil {
			return nil, err
		}

		if assetVehicle.VehicleID != req.AssetVehicle.VehicleID {
			err = uc.AssetVehicleRepository.UpdateAssetVehicle(ctx, tx.DB(), &models.AssetVehicle{
				AssetID:   id,
				VehicleID: req.AssetVehicle.VehicleID,
			})
			if err != nil {
				return nil, err
			}
		}
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	// SEND NOTIFICATION
	go uc.notifyAfterUpdateAsset(contexthelpers.WithoutCancel(ctx), id, claim.UserID)

	if len(newAssignee) != 0 {
		go uc.notifyAfterAssignAsset(contexthelpers.WithoutCancel(ctx), id, newAssignee)
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}
func (uc *AssetUseCase) CreateAssetManagement(ctx context.Context, req dtos.CreateUpdateAsset) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	asset, err := uc.constructAssetModel(req)
	if err != nil {
		return nil, err
	}
	asset.ClientID = claim.GetLoggedInClientID()
	userIds, err := uc.constructUserIds(ctx, req)
	if err != nil {
		return nil, err
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tX.Rollback()
	if req.Photo != "" {
		destPhoto, err := uc.storageUsecase.MoveAssetPhotoStorage(ctx, claim.GetLoggedInClientID(), req.Photo)
		if err != nil {
			return nil, err
		}

		asset.Photo = null.StringFrom(destPhoto)
	}
	err = uc.AssetRepository.CreateAsset(ctx, tX.DB(), asset)
	if err != nil {
		return nil, err
	}

	if len(userIds) > 0 {
		assetAssignmentGroup, err := uc.constructAsssetAssignmentGroups(asset.ID, userIds)
		if err != nil {
			return nil, err
		}
		err = uc.AssetAssignmentRepository.CreateAssetAssignmentGroup(ctx, tX.DB(), assetAssignmentGroup)
		if err != nil {
			return nil, err
		}
	}

	if req.AssetAssignmentUserID != "" {
		assetAssignment, err := uc.constructAssetAssignmet(asset.ID, req.AssetAssignmentUserID)
		if err != nil {
			return nil, err
		}
		assetAssignment.ClientID = claim.GetLoggedInClientID()
		err = uc.AssetAssignmentRepository.CreateAssetAssignment(ctx, tX.DB(), assetAssignment)
		if err != nil {
			return nil, err
		}
	}

	_, err = uc.storageUsecase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET_ATTACHMENT,
		SourceReferenceID: asset.ID,
		TargetReferenceID: "",
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	if len(req.FormFields) > 0 {
		_, err = uc.formUsecase.UpsertForm(ctx, commonmodel.FormReq{
			TemplateID:       req.TemplateID,
			ReferenceID:      asset.ID,
			FormCategoryCode: contentConstants.FORM_CATEGORY_ASSET_ADDITIONAL_CODE,
			Fields:           req.FormFields,
		})
		if err != nil {
			return nil, err
		}
	}

	if asset.CustomAssetCategoryID != "" && asset.BrandID != "" {
		err = uc.AssetVehicleUseCase.brandRepo.InsertBrandCustomCategoryMappingIgnoreConflict(ctx, tX.DB(), models.BrandCustomCategoryMapping{
			BrandID:               asset.BrandID,
			CustomAssetCategoryID: asset.CustomAssetCategoryID,
			StatusCode:            constants.BRAND_CUSTOM_CATEGORY_MAPPING_STATUS_ACTIVE,
		})

		if err != nil {
			return nil, err
		}
	}

	if req.ConvertToFleetOptimax {
		req.ConvertToFleetOptimaxData.AssetID = asset.ID
		req.ConvertToFleetOptimaxData.PartnerOwnerID = req.PartnerOwnerID
		req.ConvertToFleetOptimaxData.UseTyreOptimax = true
		_, err = uc.convertAssetGeneralToAssetOptimax(ctx, tX.DB(), req.ConvertToFleetOptimaxData)
		if err != nil {
			return nil, err
		}
	}

	if req.NeedApproval {
		referenceIDs := make(pq.StringArray, 0, 1)
		referenceIDs = append(referenceIDs, asset.ID)

		approvalRequest := approvalModels.ApprovalRequest{
			SourceCode:   approvalConstants.APPROVAL_SOURCE_CREATE_NEW_ASSET,
			RequestNotes: "",
			ReferenceIDs: referenceIDs,
			Description: fmt.Sprintf(
				"Asset Name: %s\nSerial Number: %s \nPlate Number: %s",
				asset.Name,
				asset.SerialNumber,
				asset.ReferenceNumber,
			),
		}

		err = uc.ApprovalUseCase.CreateApprovalRequest(ctx, tX.DB(), &approvalRequest)
		if err != nil {
			return nil, err
		}
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	go uc.notifyAfterAssignAsset(ctx, asset.ID, userIds)

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: asset.ID,
		Data:        nil,
	}, nil
}

func (uc *AssetUseCase) notifyAfterAssignAsset(
	ctx context.Context,
	assetId string,
	userIDs []string,
) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return
	}

	asset, err := uc.AssetRepository.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID: assetId,
		},
		Preload: models.AssetPreload{
			Brand:                  true,
			Location:               true,
			CustomAssetCategory:    true,
			CustomAssetSubCategory: true,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get asset assign notif %v", err)
		return
	}

	assetPlatNo := ""
	if asset.ReferenceNumber == "" {
		assetPlatNo = asset.SerialNumber
	} else {
		assetPlatNo = asset.ReferenceNumber
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: asset.ClientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return
	}

	assettHref := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_ASSET, asset.ID)

	templateBod := tmplhelpers.AssetAssignBod{
		AssetAssignName:   asset.Name,
		AssetAssignPlatNo: assetPlatNo,
		AssetBrand:        "-",
		AssetCategory:     "-",
		AssetSubCategory:  "-",
		AssetLocation:     "-",
		RedirectAssetLink: template.URL(assettHref),
	}
	if asset.Brand.BrandName != "" {
		templateBod.AssetBrand = asset.Brand.BrandName
	}
	if asset.CustomAssetCategory.Name != "" {
		templateBod.AssetCategory = asset.CustomAssetCategory.Name
	}
	if asset.CustomAssetSubCategory.Name != "" {
		templateBod.AssetSubCategory = asset.CustomAssetSubCategory.Name
	}
	if asset.Location.Name != "" {
		templateBod.AssetLocation = asset.Location.Name
	}

	notifItem := notificationDtos.CreateNotificationItem{
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_UPDATE,
		SourceReferenceID: asset.ID,
		TargetReferenceID: asset.ID,
		TargetURL:         assettHref,
		MessageHeader:     templateBod.ConstructSubjectEmail(),
		MessageBody:       templateBod.ConstructBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.ConstrucPushNotifSubject(),
			Body:  templateBod.ConstrucPushNotifBody(),
		},
		ClientID:        claim.GetLoggedInClientID(),
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_ASSET,
		ReferenceValue:  asset.ID,
	}

	notifItems := []notificationDtos.CreateNotificationItem{}
	for _, userID := range userIDs {
		notifItem.UserID = userID
		notifItems = append(notifItems, notifItem)
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           notifItems,
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *AssetUseCase) GetAssetManagement(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	asset, err := uc.AssetRepository.GetAssetManagement(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.AssetPreload{
			Brand:                  true,
			AssetCategory:          true,
			CustomAssetCategory:    true,
			CustomAssetSubCategory: true,
			SubCategory:            true,
			OwnershipCategory:      true,
			Location:               true,
			AssetStatus:            true,
			AssetModel:             true,
		},
	})
	if err != nil {
		return nil, err
	}

	isViewAllAsset := claim.IsHasPermission(
		constants.ASSET_PERMISSION_CATEGORY,
		constants.ASSET_PERMISSION_VIEW_ALL_LIST_ASSET,
	)

	if !isViewAllAsset {
		_, err := uc.AssetRepository.GetAssetManagement(ctx, uc.DB.DB(), models.AssetCondition{
			Where: models.AssetWhere{
				ID: id,
				NonViewAllAssetCondition: &models.NonViewAllAssetCondition{
					UserID: claim.UserID,
				},
				ClientID: claim.GetLoggedInClientID(),
			},
		})
		if err != nil && !errorhandler.IsErrNotFound(err) {
			return nil, err
		} else if errorhandler.IsErrNotFound(err) {
			return nil, errorhandler.ErrBadRequest(errorhandler.ErrNoPermission)
		}
	}

	if asset.Photo.String != "" {
		photoUrl, _ := helpers.GenerateCloudStorageSignedURL(asset.Photo.String, time.Duration(24))
		asset.Photo = null.StringFrom(photoUrl)
	}
	assetAssignmentGroups, err := uc.AssetAssignmentRepository.GetAssetAssignmentGroup(ctx, uc.DB.DB(), models.AssetAssignmentCondition{
		Where: models.AssetAssignmentWhere{
			AssetID:  id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}
	assetAssignment, err := uc.AssetAssignmentRepository.GetAssetAssignment(ctx, uc.DB.DB(), models.AssetAssignmentCondition{
		Where: models.AssetAssignmentWhere{
			AssetID:  id,
			Assigned: true,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}
	userIDs := []string{}
	for _, assetAssignmentGroup := range assetAssignmentGroups {
		userIDs = append(userIDs, assetAssignmentGroup.UserID)
	}

	if assetAssignment != nil {
		userIDs = append(userIDs, assetAssignment.UserID)
	}

	users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			IDs: userIDs,
		},
	})
	if err != nil {
		return nil, err
	}

	mapUserNames := map[string]string{}
	mapUserPhoneNumbers := map[string]string{}
	for i := range users {
		mapUserNames[users[i].ID] = users[i].GetName()
		mapUserPhoneNumbers[users[i].ID] = users[i].PhoneNumber
	}

	customAssetCategory := dtos.CustomAssetCategory{}
	customAssetCategory.Set(asset.CustomAssetCategory)

	customAssetSubCategory := dtos.CustomAssetSubCategory{}
	customAssetSubCategory.Set(asset.CustomAssetSubCategory)

	resp := dtos.GetAssetDetail{
		ID:                       asset.ID,
		CreatedBy:                asset.CreatedBy,
		CreatedAt:                asset.CreatedAt,
		UpdatedBy:                asset.UpdatedBy,
		CategoryCode:             asset.AssetCategoryCode,
		Category:                 asset.AssetCategory,
		SubCategoryCode:          asset.SubCategoryCode,
		SubCategory:              asset.SubCategory,
		CustomCategoryID:         asset.CustomAssetCategoryID,
		CustomCategory:           customAssetCategory,
		CustomSubCategoryID:      asset.CustomAssetSubCategoryID,
		CustomSubCategory:        customAssetSubCategory,
		Name:                     asset.Name,
		BrandID:                  asset.BrandID,
		BrandName:                asset.Brand.BrandName,
		SerialNumber:             asset.SerialNumber,
		ReferenceNumber:          asset.ReferenceNumber,
		OwnershipCategoryCode:    asset.OwnershipCategoryCode,
		OwnershipCategory:        asset.OwnershipCategory,
		StatusCode:               asset.AssetStatusCode,
		Status:                   asset.AssetStatus,
		LocationID:               asset.LocationID,
		LocationName:             asset.Location.Name,
		Assigned:                 dtos.Assigned{},
		AssignedTo:               []dtos.GetAssignedTo{},
		Photo:                    asset.Photo,
		Rfid:                     asset.Rfid,
		GpsImei:                  asset.GpsImei,
		HandoverFormTemplateID:   asset.HandoverFormTemplateID,
		HandoverNeedInspection:   asset.HandoverNeedInspection,
		UseFleetOptimax:          asset.UseFleetOptimax,
		UseTyreOptimax:           asset.UseTyreOptimax,
		Models:                   asset.AssetModel,
		Address:                  asset.Address,
		AddressMapLink:           asset.AddressMapLink,
		UsingPartnerOwnerAddress: asset.UsingPartnerOwnerAddress,
	}

	if assetAssignment != nil {
		resp.Assigned = dtos.Assigned{
			ID:          assetAssignment.ID,
			UserID:      assetAssignment.UserID,
			Name:        mapUserNames[assetAssignment.UserID],
			PhoneNumber: mapUserPhoneNumbers[assetAssignment.UserID],
		}
	}

	for _, assetAssignmentGroup := range assetAssignmentGroups {
		resp.AssignedTo = append(resp.AssignedTo, dtos.GetAssignedTo{
			ID:     assetAssignmentGroup.ID,
			UserID: assetAssignmentGroup.UserID,
			Name:   mapUserNames[assetAssignmentGroup.UserID],
		})
	}

	/*
		TODO: Will Refactor This
		If Vehicle Optimax Then Return Vehicle Detail
	*/
	if asset.AssetCategoryCode == constants.ASSET_CATEGORY_VEHICLE_CODE {
		assetVehicle, err := uc.AssetVehicleUseCase.GetAssetVehicleDetail(ctx, id)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err
			}
		}
		resp.AssetVehicle = assetVehicle
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        resp,
	}, nil

}

func (uc *AssetUseCase) GenerateNonViewAllAssetCondition(ctx context.Context, claim authhelpers.JwtTokenClaims) (*models.NonViewAllAssetCondition, error) {
	return &models.NonViewAllAssetCondition{
		UserID: claim.UserID,
	}, nil
}

func (uc *AssetUseCase) GetListAssetManagement(ctx context.Context, req dtos.AssetManagementListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	isViewAllAsset := claim.IsHasPermission(
		constants.ASSET_PERMISSION_CATEGORY,
		constants.ASSET_PERMISSION_VIEW_ALL_LIST_ASSET,
	)
	var nonViewAllAssetCondition *models.NonViewAllAssetCondition
	if !isViewAllAsset {
		var err error
		nonViewAllAssetCondition, err = uc.GenerateNonViewAllAssetCondition(ctx, claim)
		if err != nil {
			return nil, err
		}
	}

	count, assets, err := uc.AssetRepository.GetAssetManagementList(ctx, uc.DB.DB(), models.GetAssetListParam{
		ListRequest: req.ListRequest,
		Cond: models.AssetCondition{
			Where: models.AssetWhere{
				ClientID:                  claim.GetLoggedInClientID(),
				NonViewAllAssetCondition:  nonViewAllAssetCondition,
				Statuses:                  req.StatusCodes,
				PartnerOwnerID:            req.PartnerOwnerID,
				Categories:                req.Categories,
				SubCategories:             req.SubCategories,
				CustomAssetCategoryIDs:    req.CustomCategories,
				CustomAssetSubCategoryIDs: req.CustomSubCategories,
				LocationIds:               req.LocationIds,
				CreateOn:                  req.CreateOn,
				CreatedStartDate:          req.CreatedStartDate,
				CreatedEndDate:            req.CreatedEndDate,
				OptimaxStatuses:           req.OptimaxStatuses,
				NotLinkedToAssetID:        req.NotLinkedToAssetID,
				ExcludedID:                req.NotLinkedToAssetID,
			},
			Preload: models.AssetPreload{
				AssetCategory:          true,
				Brand:                  true,
				OwnershipCategory:      true,
				Location:               true,
				AssetStatus:            true,
				SubCategory:            true,
				CustomAssetCategory:    true,
				CustomAssetSubCategory: true,
				AssetModel:             true,
				AssetAssignment:        true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	userIDs := []string{}
	for _, asset := range assets {
		if asset.AssetAssignment != nil && asset.AssetAssignment.UserID != "" {
			userIDs = append(userIDs, asset.AssetAssignment.UserID)
		}
	}

	mapUserNames := map[string]string{}
	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
			Where: userIdentityModel.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range users {
			mapUserNames[users[i].ID] = users[i].GetName()
		}
	}

	response := []dtos.GetAssetList{}
	for _, val := range assets {
		dto := dtos.GetAssetList{}
		if val.Photo.String != "" {
			photoUrl, _ := helpers.GenerateCloudStorageSignedURL(val.Photo.String, time.Duration(24))
			val.Photo = null.StringFrom(photoUrl)
		}
		dto.Set(val)
		dto.AssigneeName = mapUserNames[dto.AssigneeID]

		response = append(response, dto)
	}
	respData := response

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *AssetUseCase) GetAssets(ctx context.Context, req dtos.AssetListReq) (commonmodel.ListResponse, error) {
	assetsResponse := commonmodel.ListResponse{}
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return assetsResponse, err
	}

	totalRecords, assets, err := uc.AssetRepository.GetAssetList(ctx, uc.DB.DB(), models.GetAssetListParam{
		ListRequest: req.ListRequest,
		Cond: models.AssetCondition{
			Where: models.AssetWhere{
				ClientID: claim.GetLoggedInClientID(),
				Statuses: req.StatusCodes,
			},
		},
	})
	if err != nil {
		return assetsResponse, err
	}

	if len(assets) == 0 {
		return assetsResponse, nil
	}

	newResponse := dtos.BuildAssetListResponse(assets)
	assetsResponse = commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         newResponse,
	}

	return assetsResponse, nil
}

func (uc *AssetUseCase) GetAssetsByAdmin(ctx context.Context, req dtos.AssetListReq) (commonmodel.ListResponse, error) {
	assetsResponse := commonmodel.ListResponse{}

	totalRecords, assets, err := uc.AssetRepository.GetAssetList(ctx, uc.DB.DB(), models.GetAssetListParam{
		ListRequest: req.ListRequest,
		Cond: models.AssetCondition{
			Where: models.AssetWhere{
				Statuses: req.StatusCodes,
				ClientID: req.ClientID,
			},
		},
	})
	if err != nil {
		return assetsResponse, err
	}

	if len(assets) == 0 {
		return assetsResponse, nil
	}

	newResponse := dtos.BuildAssetListResponse(assets)
	assetsResponse = commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         newResponse,
	}

	return assetsResponse, nil
}

func (uc *AssetUseCase) GetAssetsSelections(ctx context.Context, req dtos.GetAssetSelectionsReq) (commonmodel.ListResponse, error) {
	assetsResponse := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return assetsResponse, err
	}

	// TODO: need to simplify to safe role on claim
	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID: claim.UserID,
		},
	})
	if err != nil {
		return assetsResponse, nil
	}

	userID := ""
	if user.UserRoleCode == userConstants.USER_ROLE_CODE_DRIVER {
		userID = claim.UserID
	}

	totalRecords, assets, err := uc.AssetRepository.GetAssetSelectionsList(ctx, uc.DB.DB(), models.GetAssetListParam{
		ListRequest: req.ListRequest,
		Cond: models.AssetCondition{
			Where: models.AssetWhere{
				ClientID:           claim.GetLoggedInClientID(),
				AssignUserID:       userID,
				AssetIds:           req.AssetIDs,
				Categories:         req.AssetCategories,
				ExcludedCategories: req.ExcludedAssetCategories,
				IsWorkshop:         req.IsWorkshop,
			},
		},
	})
	if err != nil {
		return assetsResponse, err
	}

	if len(assets) == 0 {
		return assetsResponse, nil
	}

	newResponse := dtos.BuildAssetSelectionListResponse(assets)
	assetsResponse = commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         newResponse,
	}

	return assetsResponse, nil
}

func (ai *AssetUseCase) GetAssetVehicleByID(ctx context.Context, id string) (*models.AssetVehicle, error) {
	assetVehicle, err := ai.AssetVehicleRepository.GetAssetVehicleByID(ctx, ai.DB.DB(), id)
	if err != nil {
		return nil, err
	}
	return assetVehicle, nil
}

func (ai *AssetUseCase) GetAssetByID(ctx context.Context, id string) (*models.Asset, error) {
	asset, err := ai.AssetRepository.GetAssetByID(ctx, ai.DB.DB(), id)
	if err != nil {
		return nil, err
	}
	return asset, nil
}

func (ai *AssetUseCase) GetAssetsByIDs(ctx context.Context, ids []string) ([]models.Asset, error) {
	assets, err := ai.AssetRepository.GetAssetsByIDs(ctx, ai.DB.DB(), ids)
	if err != nil {
		return nil, err
	}
	return assets, nil
}

func (ai *AssetUseCase) GetAssetsMapByID(ctx context.Context, assetMapById *map[string]models.Asset, ids []string) error {
	assets, err := ai.AssetRepository.GetAssetsByIDs(ctx, ai.DB.DB(), ids)

	if err != nil {
		return err
	}

	if *assetMapById == nil {
		*assetMapById = make(map[string]models.Asset)
	}

	for _, asset := range assets {
		(*assetMapById)[asset.ID] = asset
	}

	return nil
}

// Move this to utils
func parseDate(dateString string) time.Time {
	layout := "2006-01-02"
	parsedDate, _ := time.Parse(layout, dateString)
	return parsedDate
}

func (ai *AssetUseCase) GetAssetBrands(ctx context.Context, brandTags []string) ([]models.Brand, error) {
	var brands []models.Brand
	err := ai.AssetRepository.GetAssetBrands(ctx, ai.DB.DB(), &brands, brandTags)
	if err != nil {
		return nil, err
	}
	return brands, nil
}

func checkUpdateAsset(
	asset *models.Asset,
	updateAsset *models.Asset,
	logMapPrev map[string]interface{},
	logMapNew map[string]interface{},
) bool {
	isAnyUpdate := false
	updatedAsset := &models.Asset{}
	updatedAsset.ID = asset.ID

	if asset.Name != updateAsset.Name {
		isAnyUpdate = true
		updatedAsset.Name = updateAsset.Name
		logMapPrev["Name"] = asset.Name
		logMapNew["Name"] = updatedAsset.Name
	}

	if asset.BrandID != updateAsset.BrandID {
		isAnyUpdate = true
		updatedAsset.BrandID = updateAsset.BrandID
		logMapPrev["Brand ID"] = asset.BrandID
		logMapNew["Brand ID"] = updatedAsset.BrandID
	}

	if asset.ModelNumber != updateAsset.ModelNumber {
		isAnyUpdate = true
		updatedAsset.ModelNumber = updateAsset.ModelNumber
		logMapPrev["Model number"] = asset.ModelNumber
		logMapNew["Model number"] = updatedAsset.ModelNumber
	}

	if asset.SerialNumber != updateAsset.SerialNumber {
		isAnyUpdate = true
		updatedAsset.SerialNumber = updateAsset.SerialNumber
		logMapPrev["Serial number"] = asset.SerialNumber
		logMapNew["Serial number"] = updatedAsset.SerialNumber
	}

	if asset.AssetStatusCode != updateAsset.AssetStatusCode {
		isAnyUpdate = true
		updatedAsset.AssetStatusCode = updateAsset.AssetStatusCode
		logMapPrev["Status"] = asset.AssetStatusCode
		logMapNew["Status"] = updatedAsset.AssetStatusCode
	}

	if asset.ProductionDate.Year() != updateAsset.ProductionDate.Year() {
		isAnyUpdate = true
		updatedAsset.ProductionDate = updateAsset.ProductionDate
		logMapPrev["Production year"] = asset.ProductionDate
		logMapNew["Production year"] = updatedAsset.ProductionDate
	}

	if asset.Cost != updateAsset.Cost {
		isAnyUpdate = true
		updatedAsset.Cost = updateAsset.Cost
		logMapPrev["Cost"] = asset.Cost
		logMapNew["Cost"] = updatedAsset.Cost
	}

	if asset.Rfid != updateAsset.Rfid {
		isAnyUpdate = true
		updatedAsset.Rfid = updateAsset.Rfid
		logMapPrev["RFID"] = asset.Rfid
		logMapNew["RFID"] = updatedAsset.Rfid
	}

	*updateAsset = *updatedAsset

	return isAnyUpdate
}

func isValidStatusCode(status string) bool {
	return status == constants.ASSET_STATUS_CODE_ACTIVE ||
		status == constants.ASSET_STATUS_CODE_INSTALLED ||
		status == constants.ASSET_STATUS_CODE_INACTIVE ||
		status == constants.ASSET_STATUS_CODE_IN_ARCHIVED ||
		status == constants.ASSET_STATUS_CODE_NEW_STOCK ||
		status == constants.ASSET_STATUS_CODE_IN_REPAIR ||
		status == constants.ASSET_STATUS_CODE_IN_STOCK ||
		status == constants.ASSET_STATUS_CODE_DISPOSED ||
		status == constants.ASSET_STATUS_CODE_DISPOSE_PENDING ||
		status == constants.ASSET_STATUS_CODE_MAINTENANCE
}

func (uc *AssetUseCase) UpdateAssetStatus(ctx context.Context, assetID string, status string) error {
	_, err := uc.AssetRepository.GetAssetStatusByCode(ctx, uc.DB.DB(), status)
	if err != nil {
		return err
	}

	asset, err := uc.AssetRepository.GetAssetByID(ctx, uc.DB.DB(), assetID)
	if err != nil {
		commonlogger.Warnf("error while getting asset", err)
		return err
	}

	if asset.AssetStatusCode == status {
		return nil
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return err
	}

	defer tX.Rollback()

	updateAsset := &models.Asset{
		AssetStatusCode: status,
	}

	updateAsset.ID = assetID
	err = uc.AssetRepository.UpdateAsset(ctx, tX.DB(), updateAsset)
	if err != nil {
		return err
	}

	if asset.AssetCategoryCode == constants.ASSET_CATEGORY_TYRE_CODE {
		if status == constants.ASSET_STATUS_CODE_IN_REPAIR {
			err = uc.AssetTryeRepository.IncreaseAssetTyreRepairedNumber(ctx, tX.DB(), assetID)
			if err != nil {
				return err
			}
		}

		if status == constants.ASSET_STATUS_CODE_IN_STOCK {
			err = uc.AssetTryeRepository.UpdateAssetTyre(ctx, tX.DB(), &models.AssetTyre{
				AssetID:       assetID,
				LastStockDate: time.Now().In(time.UTC),
			})
			if err != nil {
				return err
			}
		}
	}

	err = tX.Commit()
	if err != nil {
		return err
	}

	go uc.notifyAfterUpdateAssetStatus(contexthelpers.WithoutCancel(ctx), assetID, asset.StatusLabel())
	return nil
}

func (uc *AssetUseCase) notifyAfterUpdateAssetStatus(
	ctx context.Context,
	assetID string,
	prevStatusLabel string,
) {
	assetAssignment, err := uc.AssetAssignmentRepository.GetAssetAssignment(
		ctx, uc.DB.DB(), models.AssetAssignmentCondition{
			Where: models.AssetAssignmentWhere{
				AssetID:  assetID,
				Assigned: true,
			},
			Preload: models.AssetAssignmentPreload{
				Asset:      true,
				AssetBrand: true,
			},
		})
	if err != nil {
		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Warnf("failed to get asset assignment %v", err)
		}
		return
	}

	switch assetAssignment.Asset.AssetCategoryCode {
	case constants.ASSET_CATEGORY_TYRE_CODE:
		go uc.notifyAfterUpdateAssetTyreStatus(contexthelpers.WithoutCancel(ctx), assetAssignment, prevStatusLabel)
	case constants.ASSET_CATEGORY_VEHICLE_CODE:
		go uc.notifyAfterUpdateAssetVehicleStatus(contexthelpers.WithoutCancel(ctx), assetAssignment, prevStatusLabel)
	}

}

func (uc *AssetUseCase) notifyAfterUpdateAsset(
	ctx context.Context,
	assetID string,
	registrantUserID string,
) {
	// FETCH ASSIGNMENT DATA
	assetAssignment, err := uc.AssetAssignmentRepository.GetAssetAssignment(
		ctx, uc.DB.DB(), models.AssetAssignmentCondition{
			Where: models.AssetAssignmentWhere{
				AssetID:  assetID,
				Assigned: true,
			},
			Preload: models.AssetAssignmentPreload{
				Asset:      true,
				AssetBrand: true,
			},
		})
	if err != nil {
		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Warnf("failed to get asset assignment %v", err)
		}
		return
	}

	// FETCH REGISTRANT USER
	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID: assetAssignment.UserID,
		},
	})
	if err != nil {
		commonlogger.Warnf("error get user on notify asset update", err)
		return
	}

	// FETCH CLIENT DATA
	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: assetAssignment.ClientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return
	}

	// CONSTRUCT DATA
	assetName := ""
	if assetAssignment.Asset.ReferenceNumber == "" {
		assetName = fmt.Sprintf("%s - %s", assetAssignment.Asset.Name, assetAssignment.Asset.SerialNumber)
	} else {
		assetName = fmt.Sprintf("%s - %s", assetAssignment.Asset.Name, assetAssignment.Asset.ReferenceNumber)
	}
	dateOfChange := assetAssignment.Asset.UpdatedAt.Format("2 January 2006 15:04:05")
	href := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_ASSET, assetAssignment.AssetID)

	// EMAIL MSG
	title, err := tmplhelpers.ParseStringTemplate(
		"Notification: Asset Information Updated for {{.assetName}}",
		map[string]interface{}{
			"assetName": assetName,
		})
	if err != nil {
		commonlogger.Warnf("failed to create title %v", err)
		return
	}

	body, err := tmplhelpers.ParseStringTemplate(
		`We wanted to let you know that the information for your asset has been updated:<br><br>
		Asset: {{.assetName}} <br>
		Updated By: {{.updatedBy}} <br>
		Date of Change: {{.date}} <br><br>
		For more information about the asset, please click below: <br><br>
		<a href="{{.assetHref}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Asset Detail</button></a>`,
		map[string]interface{}{
			"assetName": assetName,
			"updatedBy": user.GetName(),
			"date":      dateOfChange,
			"assetHref": template.URL(href),
		})
	if err != nil {
		commonlogger.Warnf("failed to create body %v", err)
		return
	}

	// FIREBASE MSG
	titleFirebase := "Asset Info Edited"
	bodyFirebase := fmt.Sprintf("The asset %s has been updated by %s.", assetName, user.GetName())

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            assetAssignment.UserID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_ASSET,
		SourceReferenceID: assetAssignment.AssetID,
		TargetReferenceID: "",
		TargetURL:         href,
		MessageHeader:     title,
		MessageBody:       body,
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: titleFirebase,
			Body:  bodyFirebase,
		},
		ClientID:        assetAssignment.ClientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_ASSET,
		ReferenceValue:  assetAssignment.AssetID,
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *AssetUseCase) constructNewAssetStatusNotifItem(
	ctx context.Context,
	assetAssignment *models.AssetAssignment,
	notifItem notificationDtos.CreateNotificationItem,
) notificationDtos.CreateNotificationItem {
	// FETCH ADDITIONAL DATA
	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID: assetAssignment.UserID,
		},
	})
	if err != nil {
		commonlogger.Warnf("error get user on notify near expired asset transaction", err)
		return notifItem
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: assetAssignment.ClientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return notifItem
	}

	// CONSTRUCT DATA
	assetName := ""
	if assetAssignment.Asset.ReferenceNumber == "" {
		assetName = fmt.Sprintf("%s - %s", assetAssignment.Asset.Name, assetAssignment.Asset.SerialNumber)
	} else {
		assetName = fmt.Sprintf("%s - %s", assetAssignment.Asset.Name, assetAssignment.Asset.ReferenceNumber)
	}

	markAsLabel := ""
	changeStatusMsg := ""
	if assetAssignment.Asset.StatusLabel() == constants.ASSET_STATUS_LABEL_INACTIVE {
		markAsLabel = "Not Active"
		changeStatusMsg = "was marked as inactive"
	} else {
		markAsLabel = "Under Maintenance"
		changeStatusMsg = "is set to Maintenance"
	}

	dateOfChange := assetAssignment.Asset.UpdatedAt.Format("2 January 2006 15:04:05")
	href := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_ASSET, assetAssignment.AssetID)

	// EMAIL MSG
	title, err := tmplhelpers.ParseStringTemplate(
		"Asset Status Update: {{.assetName}} Marked as {{.markAsLabel}}",
		map[string]interface{}{
			"assetName":   assetName,
			"markAsLabel": markAsLabel,
		})
	if err != nil {
		commonlogger.Warnf("failed to create title %v", err)
		return notifItem
	}

	body, err := tmplhelpers.ParseStringTemplate(
		`We wanted to inform you that the status of the following asset has been marked as <b>{{.statusLabel}}</b><br><br>
		Asset: {{.assetName}} <br>
		Marked By: {{.markedBy}} <br>
		Date of Change: {{.date}} <br><br>
		For more information about the asset, please click below: <br><br>
		<a href="{{.assetHref}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Asset Detail</button></a>`,
		map[string]interface{}{
			"statusLabel": assetAssignment.Asset.StatusLabel(),
			"assetName":   assetName,
			"markedBy":    user.GetName(),
			"date":        dateOfChange,
			"assetHref":   template.URL(href),
		})
	if err != nil {
		commonlogger.Warnf("failed to create body %v", err)
		return notifItem
	}

	// FIREBASE MSG
	titleFirebase := fmt.Sprintf("Asset Marked as %s", markAsLabel)
	bodyFirebase := fmt.Sprintf("The asset %s %s by %s.", assetName, changeStatusMsg, user.GetName())

	// EXTEND DATA TO NOTIF ITEM
	notifItem.MessageHeader = title
	notifItem.MessageBody = body
	notifItem.MessageFirebase = notificationDtos.MessageFirebase{
		Title: titleFirebase,
		Body:  bodyFirebase,
	}

	return notifItem
}

func (uc *AssetUseCase) notifyAfterUpdateAssetTyreStatus(
	ctx context.Context,
	assetAssignment *models.AssetAssignment,
	prevStatus string,
) {
	assetTyre, err := uc.AssetTryeRepository.GetAssetTyre(ctx, uc.DB.DB(), models.AssetTyreCondition{
		Where: models.AssetTyreWhere{
			AssetID: assetAssignment.AssetID,
		},
		Preload: models.AssetTyrePreload{
			Tyre: true,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get tyre, err: %v", err)
		return
	}

	title, err := tmplhelpers.ParseStringTemplate(
		"Tyre {{.serialNumber}} Status Changed to {{.statusLabel}}",
		map[string]interface{}{
			"serialNumber": assetAssignment.Asset.SerialNumber,
			"statusLabel":  assetAssignment.Asset.StatusLabel(),
		})
	if err != nil {
		commonlogger.Warnf("failed to create title %v", err)
		return
	}

	body, err := tmplhelpers.ParseStringTemplate(
		`Asset tyre status updated to {{.statusLabel}}<br><br>
		Previous Status: {{.prevStatusLabel}} <br>
		New Status: {{.statusLabel}} <br>
		Serial Number: {{.serialNumber}} <br>
		Brand: {{.brandName}} <br>
		Pattern/Type: {{.patternType}} <br>`,
		map[string]interface{}{
			"statusLabel":     assetAssignment.Asset.StatusLabel(),
			"prevStatusLabel": prevStatus,
			"serialNumber":    assetAssignment.Asset.SerialNumber,
			"brandName":       assetAssignment.Asset.Brand.BrandName,
			"patternType":     assetTyre.Tyre.PatternType,
		})
	if err != nil {
		commonlogger.Warnf("failed to create body %v", err)
		return
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            assetAssignment.UserID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_ASSET_TYRE,
		SourceReferenceID: assetAssignment.AssetID,
		TargetReferenceID: "",
		TargetURL:         "",
		MessageHeader:     title,
		MessageBody:       body,
		ClientID:          assetAssignment.ClientID,
		TypeCode:          "",
		ContentTypeCode:   "",
		ReferenceCode:     notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:    assetAssignment.AssetID,
	}

	// INACTIVE AND MAINTENANCE NOTIFICATION ADJUSTMENTS
	if assetAssignment.Asset.AssetStatusCode == constants.ASSET_STATUS_CODE_INACTIVE || assetAssignment.Asset.AssetStatusCode == constants.ASSET_STATUS_CODE_MAINTENANCE {
		notifItem = uc.constructNewAssetStatusNotifItem(
			ctx,
			assetAssignment,
			notifItem,
		)
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: true,
	})

}

func (uc *AssetUseCase) notifyAfterUpdateAssetVehicleStatus(
	ctx context.Context,
	assetAssignment *models.AssetAssignment,
	prevStatus string,
) {
	// assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, uc.DB.DB(), models.AssetVehicleCondition{
	// 	Where: models.AssetVehicleWhere{
	// 		AssetID: assetAssignment.AssetID,
	// 	},
	// })
	// if err != nil {
	// 	commonlogger.Warnf("failed to get tyre, err: %v", err)
	// 	return
	// }

	title, err := tmplhelpers.ParseStringTemplate(
		"Vehicle {{.vehicleName}} /. {{.serialNumber}} Status Changed to {{.statusLabel}}",
		map[string]interface{}{
			"vehicleName":  assetAssignment.Asset.Name,
			"serialNumber": assetAssignment.Asset.SerialNumber,
			"statusLabel":  assetAssignment.Asset.StatusLabel(),
		})
	if err != nil {
		commonlogger.Warnf("failed to create title %v", err)
		return
	}

	assetVehicleTable, err := tmplhelpers.ParseStringTemplate(
		`<table>
		{{ range $key, $value := . }}
			<tr>
			<td>{{ $key }}</td>: <td>{{ $value }}</td>
			</tr>
		{{ end }}
		</table>
		<br>`,
		map[string]interface{}{
			"Prev Status":   prevStatus,
			"New Status":    assetAssignment.Asset.StatusLabel(),
			"Serial Number": assetAssignment.Asset.SerialNumber,
			"Brand Name":    assetAssignment.Asset.Brand.BrandName,
			// "Model":         assetVehicle..PatternType,
		},
	)
	if err != nil {
		commonlogger.Warnf("failed to create assetVehicleTable for email %v", err)
		return
	}

	body, err := tmplhelpers.ParseStringTemplate(
		`Asset vehicle status updated to {{.statusLabel}}<br><br>`+assetVehicleTable,
		map[string]interface{}{
			"statusLabel": assetAssignment.Asset.StatusLabel(),
		})
	if err != nil {
		commonlogger.Warnf("failed to create body %v", err)
		return
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            assetAssignment.UserID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_ASSET_VEHICLE,
		SourceReferenceID: assetAssignment.AssetID,
		TargetReferenceID: "",
		TargetURL:         "",
		MessageHeader:     title,
		MessageBody:       body,
		ClientID:          assetAssignment.ClientID,
		TypeCode:          "",
		ContentTypeCode:   "",
		ReferenceCode:     notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:    assetAssignment.AssetID,
	}

	// INACTIVE AND MAINTENANCE NOTIFICATION ADJUSTMENTS
	if assetAssignment.Asset.AssetStatusCode == constants.ASSET_STATUS_CODE_INACTIVE || assetAssignment.Asset.AssetStatusCode == constants.ASSET_STATUS_CODE_MAINTENANCE {
		notifItem = uc.constructNewAssetStatusNotifItem(
			ctx,
			assetAssignment,
			notifItem,
		)
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: true,
	})

}

func (uc *AssetUseCase) GetAssetLogList(ctx context.Context, assetID string, req commonmodel.ListRequest) (*commonmodel.ListResponse, error) {

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, logs, err := uc.AssetLogRepo.GetAssetLogList(ctx, uc.DB.DB(), models.GetAssetLogListParam{
		ListRequest: req,
		Cond: models.AssetLogCondition{
			Where: models.AssetLogWhere{
				AssetID:  assetID,
				ClientID: claim.GetLoggedInClientID(),
			},
		},
	})
	if err != nil {
		return nil, err
	}

	userIDs := make([]string, 0, len(logs))
	for _, log := range logs {
		userIDs = append(userIDs, log.UpdatedByUserID)
	}
	usersMap := map[string]userIdentityModel.User{}
	uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMap, userIDs)

	logsResp := make([]dtos.AssetLogRespItem, 0, len(logs))
	for _, log := range logs {
		logsResp = append(logsResp, dtos.AssetLogRespItem{
			UpdatedByUserID: log.UpdatedByUserID,
			UserName:        usersMap[log.UpdatedByUserID].GetName(),
			AssetID:         log.AssetID,
			PreviousValue:   log.PreviousValue,
			NewValue:        log.NewValue,
			Type:            log.Type,
		})
	}

	resp := &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         logsResp,
	}

	return resp, nil
}

func (uc *AssetUseCase) AssetWithTimer(ctx context.Context, dB database.DBI, asset *models.Asset, updatedAsset *models.Asset) error {
	if updatedAsset.AssetStatusCode == constants.ASSET_STATUS_CODE_ACTIVE && asset != nil {
		if asset.StatusInactiveStartTime != nil && asset.StatusInactiveStartTime.Valid {
			updatedAsset.StatusInactiveTotalTime = asset.StatusInactiveTotalTime + int(time.Since(asset.StatusInactiveStartTime.Time).Seconds())
		}
		updatedAsset.StatusInactiveStartTime = &sql.NullTime{}
	}
	if updatedAsset.AssetStatusCode == constants.ASSET_STATUS_CODE_INACTIVE {
		startTime := sqlhelpers.NullTime(time.Now())
		updatedAsset.StatusInactiveStartTime = &startTime
	}
	return nil
}

func (uc *AssetUseCase) constructVehicleOptimax(req dtos.ConvertToOptimax) (*models.AssetVehicle, error) {
	asset := models.AssetVehicle{}
	asset.AssetID = req.AssetID
	asset.VehicleID = req.VehicleID

	asset.UseKilometer = req.MeterCalculationKM
	asset.UseHourmeter = req.MeterCalculationHM

	totalTyres, totalSpareTyres := utils.CountAxleConfigurationTyres(models.AxleConfigurationNewToOld(req.AxleConfiguration))
	asset.NumberOfTyres = null.IntFrom(int64(totalTyres))
	asset.NumberOfSpareTyres = null.IntFrom(int64(totalSpareTyres))
	asset.AxleConfiguration = req.AxleConfiguration
	asset.MaxRtdDiffTolerance = &req.MaxRtdDiffTolerance
	return &asset, nil
}

// Deprecated
func (uc *AssetUseCase) validateAlreadyOptimax(ctx context.Context, req dtos.ConvertToOptimax) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}
	vehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, uc.DB.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			AssetID:  req.AssetID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil && !errors.Is(err, errorhandler.ErrDataNotFound("asset vehicle")) {
		return err
	}
	if vehicle != nil {
		return errorhandler.ErrBadRequest("Asset already converted to vehicle optimax")
	}
	return nil
}

func (uc *AssetUseCase) validateAssetToOptimax(ctx context.Context, tX database.DBI, req dtos.ConvertToOptimax) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	// Meter calculation must be same
	if req.MeterCalculationHM == req.MeterCalculationKM {
		return errorhandler.ErrBadRequest("Wrong optimax meter calculation unit")
	}

	asset, err := uc.AssetRepository.GetAsset(ctx, tX, models.AssetCondition{
		Where: models.AssetWhere{
			ID:             req.AssetID,
			ClientID:       claim.GetLoggedInClientID(),
			PartnerOwnerID: req.PartnerOwnerID,
		},
	})
	if err != nil {
		return err
	}

	if asset.UseFleetOptimax.Bool {
		return errorhandler.ErrBadRequest("Asset is already optimax")
	}

	switch asset.AssetCategoryCode {
	case constants.ASSET_CATEGORY_VEHICLE_CODE,
		constants.ASSET_CATEGORY_EQUIPMENT_CODE:
		return nil
	default:
		return errorhandler.ErrBadRequest("Asset category is not Vehicle or Equipment")
	}
}

func (uc *AssetUseCase) updateAssetUseOptimax(ctx context.Context, dB database.DBI, assetID string, useFleetOptimax bool, useTyreOptimax bool) error {
	asset := models.Asset{}
	asset.ID = assetID
	asset.UseFleetOptimax = null.BoolFrom(useFleetOptimax)
	asset.UseTyreOptimax = null.BoolFrom(useTyreOptimax)
	err := uc.AssetRepository.UpdateAsset(ctx, dB, &asset)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AssetUseCase) validateOptimaxToGeneral(ctx context.Context, req dtos.ConvertFromOptimax) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}
	asset, err := uc.AssetRepository.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID:       req.AssetID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return err
	}
	_, err = uc.AssetVehicleRepository.GetAssetVehicle(ctx, uc.DB.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			AssetID:  req.AssetID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return err
	}
	switch asset.AssetCategoryCode {
	case constants.ASSET_CATEGORY_VEHICLE_CODE:
		return nil
	default:
		return errorhandler.ErrBadRequest("Asset category is not vehicle optimax")
	}
}

func (uc *AssetUseCase) ConvertAssetGeneralToAssetOptimax(ctx context.Context, req dtos.ConvertToOptimax) (*commonmodel.CreateResponse, error) {
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tX.Rollback()

	resp, err := uc.convertAssetGeneralToAssetOptimax(ctx, tX.DB(), req)
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (uc *AssetUseCase) convertAssetGeneralToAssetOptimax(ctx context.Context, tX database.DBI, req dtos.ConvertToOptimax) (*commonmodel.CreateResponse, error) {
	err := uc.validateAssetToOptimax(ctx, tX, req)
	if err != nil {
		return nil, err
	}
	assetOptimax, err := uc.constructVehicleOptimax(req)
	if err != nil {
		return nil, err
	}

	err = uc.updateAssetUseOptimax(ctx, tX, req.AssetID, true, req.UseTyreOptimax)

	if err != nil {
		return nil, err
	}

	_, err = uc.AssetVehicleRepository.GetAssetVehicle(ctx, tX, models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			AssetID: req.AssetID,
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if errorhandler.IsErrNotFound(err) {
		err = uc.AssetVehicleRepository.CreateAssetVehicle(ctx, tX, assetOptimax)
		if err != nil {
			return nil, err
		}
	} else {
		err = uc.AssetVehicleRepository.UpdateAssetVehicle(ctx, tX, assetOptimax)
		if err != nil {
			return nil, err
		}
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: req.AssetID,
		Data:        nil,
	}, nil
}

func (uc *AssetUseCase) ConvertAssetOptimaxToAssetGeneral(ctx context.Context, req dtos.ConvertFromOptimax) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		commonlogger.Warnf("error ConvertAssetOptimaxToAssetGeneral, %v", err)
		return nil, err
	}

	_, err = uc.AssetRepository.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID:       req.AssetID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	assetVehicle, err := uc.AssetVehicleRepository.GetAssetVehicle(ctx, uc.DB.DB(), models.AssetVehicleCondition{
		Where: models.AssetVehicleWhere{
			AssetID:  req.AssetID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	err = uc.AssetRepository.UpdateAsset(ctx, tX.DB(), &models.Asset{
		ModelV2:         commonmodel.ModelV2{ID: req.AssetID},
		UseFleetOptimax: null.BoolFrom(false),
		UseTyreOptimax:  null.BoolFrom(false),
		DowngradeReason: req.DowngradeReason,
	})
	if err != nil {
		return nil, err
	}

	blankAxle := pgtype.JSONB{}
	blankAxle.Set("[]")

	err = uc.AssetVehicleRepository.UpdateAssetVehicle(ctx, tX.DB(), &models.AssetVehicle{
		AssetID:            req.AssetID,
		UseKilometer:       null.BoolFrom(false),
		UseHourmeter:       null.BoolFrom(false),
		AxleConfiguration:  blankAxle,
		NumberOfTyres:      null.IntFrom(0),
		NumberOfSpareTyres: null.IntFrom(0),
	})
	if err != nil {
		return nil, err
	}

	err = uc.IntegrationRepo.DeActivateIntegrationByRefID(ctx, tX.DB(), req.AssetID)
	if err != nil {
		return nil, err
	}

	err = uc.UnlinkAllTyrePyParent(ctx, tX, req.AssetID, assetVehicle)
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}
	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: req.AssetID,
		Data:        nil,
	}, nil
}

func (uc *AssetUseCase) UnlinkAllTyrePyParent(ctx context.Context, tX database.DBUsecase, parentAssetID string, assetVehicle *models.AssetVehicle) error {
	now := time.Now().UTC()
	assetLinkeds, err := uc.AssetLinkedRepo.GetAssetLinkeds(ctx, tX.DB(), models.AssetLinkedCondition{
		Where: models.AssetLinkedWhere{
			ParentAssetID: parentAssetID,
			TypeCode:      constants.ASSET_LINKED_TYPE_VEHICLE_TYRE,
		},
		Preload: models.AssetLinkedPreload{
			AssetLinkedAssetVehicleTyre: true,
			ChildAsset:                  true,
		},
		IsForUpdate: true,
	})
	if err != nil {
		return err
	}

	for _, assetLinked := range assetLinkeds {
		err := uc.AssetLinkedRepo.UpdateAssetLinkedV2(ctx, tX.DB(), assetLinked.ID, &models.AssetLinked{UnlinkedDatetime: &now})
		if err != nil {
			return err
		}

		if assetLinked.AssetLinkedAssetVehicleTyre == nil {
			continue
		}

		updateAssetLinkedVehicleTyre := &models.AssetLinkedAssetVehicleTyre{
			AssetLinkedID:       assetLinked.ID,
			OnUnlinkedVehicleKm: assetLinked.AssetLinkedAssetVehicleTyre.OnUnlinkedVehicleKm,
			OnUnlinkedVehicleHm: assetLinked.AssetLinkedAssetVehicleTyre.OnUnlinkedVehicleHm,
			OnUnlinkedTyreKm:    assetLinked.AssetLinkedAssetVehicleTyre.OnLinkedTyreKm,
			OnUnlinkedTyreHm:    assetLinked.AssetLinkedAssetVehicleTyre.OnLinkedTyreHm,
		}

		if assetLinked.AssetLinkedAssetVehicleTyre.TyrePosition <= int(assetVehicle.NumberOfTyres.Int64) {
			updateAssetLinkedVehicleTyre.OnUnlinkedVehicleKm = int(assetVehicle.VehicleKM)
			updateAssetLinkedVehicleTyre.OnUnlinkedVehicleHm = assetVehicle.VehicleHm
			updateAssetLinkedVehicleTyre.OnUnlinkedTyreKm = assetLinked.ChildAsset.TotalKM
			updateAssetLinkedVehicleTyre.OnUnlinkedTyreHm = assetLinked.ChildAsset.TotalKM

			updateAssetTyre := &models.AssetTyre{}
			updateAssetTyre.AssetID = assetLinked.ChildAssetID
			updateAssetTyre.TotalLifetime = assetLinked.ChildAsset.TotalLifetime + int(now.Sub(assetLinked.LinkedDatetime).Seconds())
			err = uc.AssetTyreRepo.UpdateAssetTyre(ctx, tX.DB(), updateAssetTyre)
			if err != nil {
				return err
			}
		}

		err = uc.AssetRepository.UpdateAsset(ctx, tX.DB(), &models.Asset{
			ModelV2:         commonmodel.ModelV2{ID: assetLinked.ChildAssetID},
			AssetStatusCode: constants.ASSET_STATUS_CODE_IN_STOCK,
		})
		if err != nil {
			return err
		}

		err = uc.AssetLinkedRepo.UpdateAssetLinkedAssetVehicleTyreV2(ctx, tX.DB(), updateAssetLinkedVehicleTyre)
		if err != nil {
			return err
		}

	}

	return nil

}

func (uc *AssetUseCase) GetAssetCategories(ctx context.Context) (*commonmodel.ListResponse, error) {
	assetCategories, err := uc.AssetRepository.GetAssetCategories(ctx, uc.DB.DB(), models.AssetCategoryCondition{})
	if err != nil {
		return nil, err
	}
	return &commonmodel.ListResponse{
		TotalRecords: len(assetCategories),
		PageSize:     len(assetCategories),
		PageNo:       1,
		Data:         assetCategories,
	}, nil
}

func (uc *AssetUseCase) GetAssetSubCategories(ctx context.Context, assetCategoryCode string) (*commonmodel.ListResponse, error) {
	assetSubCategories, err := uc.AssetRepository.GetAssetCategoryMappings(ctx, uc.DB.DB(), models.AssetCategoryMappingCondition{
		Where: models.AssetCategoryMappingWhere{
			CategoryCode: assetCategoryCode,
		},
		Preload: models.AssetCategoryMappingPreload{
			SubCategory: true,
		},
	})
	if err != nil {
		return nil, err
	}
	return &commonmodel.ListResponse{
		TotalRecords: len(assetSubCategories),
		PageSize:     len(assetSubCategories),
		PageNo:       1,
		Data:         assetSubCategories,
	}, nil
}

func (uc *AssetUseCase) RfidIsExists(ctx context.Context, rfids []string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			Rfids:    rfids,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	existsRfids := make(map[string]bool)
	for _, asset := range assets {
		existsRfids[asset.Rfid] = true // if asset.Rfid not in map will be false
	}

	resp := dtos.AssetRFIDRes{}
	for _, rfid := range rfids {
		resp.Result = append(resp.Result, dtos.AssetRFID{
			RFID:    rfid,
			IsExist: existsRfids[rfid],
		})
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        resp,
	}, nil
}

func (uc *AssetUseCase) SerialNumberIsExist(ctx context.Context, serialNumbers []string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			SerialNumbers: serialNumbers,
			ClientID:      claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	existsSerialNum := make(map[string]bool)
	for _, asset := range assets {
		// if asset.Rfid not in map will be false
		existsSerialNum[asset.SerialNumber] = true
	}

	resp := dtos.AssetSerialNumberResp{}
	for _, serialnum := range serialNumbers {
		resp.Result = append(resp.Result, dtos.AssetSerialNumber{
			SerialNumber: serialnum,
			IsExist:      existsSerialNum[serialnum],
		})
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        resp,
	}, nil
}

func (uc *AssetUseCase) ReferenceNumberIsExist(ctx context.Context, req *dtos.AssetReferenceNumberReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ReferenceNumbers: req.ReferenceNumber,
			ClientID:         claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	existsReferenceNum := make(map[string]bool)
	for _, asset := range assets {
		existsReferenceNum[asset.ReferenceNumber] = true
	}

	resp := dtos.AssetSerialNumberResp{}
	for _, refereceNum := range req.ReferenceNumber {
		resp.Result = append(resp.Result, dtos.AssetSerialNumber{
			SerialNumber: refereceNum,
			IsExist:      existsReferenceNum[refereceNum],
		})
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        resp,
	}, nil
}

func (uc *AssetUseCase) GetAssetCount(ctx context.Context, parameters []string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	resp := dtos.AssetCountResp{
		AlertInLast72Hours: 0,
		Maintenance:        0,
		Active:             0,
	}

	includeAlertInLast72Hours := false
	includeMaintenance := false
	includeActive := false
	includeInactive := false
	for _, parameter := range parameters {
		switch strings.ToUpper(parameter) {
		case constants.ASSET_COUNT_ALERT_IN_LAST_72_HOURS:
			includeAlertInLast72Hours = true
		case constants.ASSET_COUNT_MAINTENANCE:
			includeMaintenance = true
		case constants.ASSET_COUNT_ACTIVE:
			includeActive = true
		case constants.ASSET_COUNT_INACTIVE:
			includeInactive = true
		}
	}

	if includeAlertInLast72Hours {
		createdAtFrom := time.Now().Add(-72 * time.Hour)
		countAlertInLast72Hours, err := uc.AlertRepository.CountAlert(ctx, uc.DB.DB(), integrationModels.AlertCondition{
			Where: integrationModels.AlertWhere{
				ClientID:        claim.GetLoggedInClientID(),
				DistinctAssetID: true,
				CreatedAtFrom:   createdAtFrom,
			},
		})
		if err != nil {
			return nil, err
		}

		resp.AlertInLast72Hours = countAlertInLast72Hours
	}

	if includeMaintenance {
		countMaintenance, err := uc.AssetRepository.CountAsset(ctx, uc.DB.DB(), models.AssetCondition{
			Where: models.AssetWhere{
				ClientID: claim.GetLoggedInClientID(),
				Statuses: []string{constants.ASSET_STATUS_CODE_MAINTENANCE},
			},
		})
		if err != nil {
			return nil, err
		}

		resp.Maintenance = countMaintenance
	}

	if includeActive {
		countActive, err := uc.AssetRepository.CountAsset(ctx, uc.DB.DB(), models.AssetCondition{
			Where: models.AssetWhere{
				ClientID: claim.GetLoggedInClientID(),
				Statuses: []string{constants.ASSET_STATUS_CODE_ACTIVE},
			},
		})
		if err != nil {
			return nil, err
		}

		resp.Active = countActive
	}

	if includeInactive {
		countInactive, err := uc.AssetRepository.CountAsset(ctx, uc.DB.DB(), models.AssetCondition{
			Where: models.AssetWhere{
				ClientID: claim.GetLoggedInClientID(),
				Statuses: []string{constants.ASSET_STATUS_CODE_INACTIVE},
			},
		})
		if err != nil {
			return nil, err
		}

		resp.Inactive = countInactive
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: claim.GetLoggedInClientID(),
		Data:        resp,
	}, nil
}
