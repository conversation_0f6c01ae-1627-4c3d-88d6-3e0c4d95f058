package usecase

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/sqlhelpers"
	"context"
)

type LocationUseCase struct {
	DB             database.DBUsecase
	locationRepo   repository.LocationRepository
	userRepo       userIdentityRepository.UserRepository
	storageUsecase *storageUsecase.AttachmentUseCase
	assetRepo      repository.AssetRepository
}

func NewLocationUseCase(
	DB database.DBUsecase,
	locationRepo repository.LocationRepository,
	userRepo userIdentityRepository.UserRepository,
	storageUsecase *storageUsecase.AttachmentUseCase,
	assetRepo repository.AssetRepository,
) *LocationUseCase {
	return &LocationUseCase{
		DB:             DB,
		locationRepo:   locationRepo,
		userRepo:       userRepo,
		storageUsecase: storageUsecase,
		assetRepo:      assetRepo,
	}
}

func (uc *LocationUseCase) GetLocations(ctx context.Context, req dtos.LocationListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, locations, err := uc.locationRepo.GetLocationList(ctx, uc.DB.DB(), models.GetLocationListParam{
		ListRequest: req.ListRequest,
		Cond: models.LocationCondition{
			Where: models.LocationWhere{
				ClientID:    claim.GetLoggedInClientID(),
				ShowDeleted: req.ShowDeleted,
				StatusCodes: req.StatusCodes,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	userIDs := []string{}
	for _, location := range locations {
		userIDs = append(userIDs, location.PICUserID)
	}
	users, err := uc.userRepo.GetUsersV2(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			IDs:      userIDs,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	mapUserNames := map[string]string{}
	for _, user := range users {
		mapUserNames[user.ID] = user.GetName()
	}

	respData := make([]dtos.Location, 0, len(locations))
	for _, location := range locations {
		respData = append(respData, dtos.Location{
			ID:                  location.ID,
			Name:                location.Name,
			IsInventoryLocation: location.IsInventoryLocation.Bool,
			Address:             location.Address,
			Floor:               location.Floor.String,
			Unit:                location.Unit.String,
			StatusCode:          location.StatusCode,
			PICUserID:           location.PICUserID,
			PICUser: dtos.LocationUser{
				ID:   location.PICUserID,
				Name: mapUserNames[location.PICUserID],
			},
			Description:    location.Description.String,
			MapLat:         location.MapLat,
			MapLong:        location.MapLong,
			CreatedAt:      location.CreatedAt,
			LocationNumber: location.LocationNumber,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *LocationUseCase) GetLocation(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	location, err := uc.locationRepo.GetLocation(ctx, uc.DB.DB(), models.LocationCondition{
		Where: models.LocationWhere{
			ID:          id,
			ClientID:    claim.GetLoggedInClientID(),
			ShowDeleted: true,
		},
	})
	if err != nil {
		return nil, err
	}

	user, err := uc.userRepo.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID:       location.PICUserID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	respData := dtos.Location{
		ID:                  location.ID,
		Name:                location.Name,
		IsInventoryLocation: location.IsInventoryLocation.Bool,
		Address:             location.Address,
		Floor:               location.Floor.String,
		Unit:                location.Unit.String,
		StatusCode:          location.StatusCode,
		PICUserID:           location.PICUserID,
		PICUser: dtos.LocationUser{
			ID:          location.PICUserID,
			Name:        user.GetName(),
			PhoneNumber: user.PhoneNumber,
		},
		Description:    location.Description.String,
		MapLat:         location.MapLat,
		MapLong:        location.MapLong,
		CreatedAt:      location.CreatedAt,
		LocationNumber: location.LocationNumber,
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        respData,
	}, nil

}

func (uc *LocationUseCase) CreateLocation(ctx context.Context, req dtos.CreateLocation) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.userRepo.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID:       req.PICUserID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	loc := &models.Location{
		Name:                req.Name,
		IsInventoryLocation: sqlhelpers.NullBool(req.IsInventoryLocation),
		Address:             req.Address,
		Floor:               sqlhelpers.NullString(req.Floor),
		Unit:                sqlhelpers.NullString(req.Unit),
		StatusCode:          req.StatusCode,
		Description:         sqlhelpers.NullString(req.Description),
		PICUserID:           req.PICUserID,
		MapLat:              req.MapLat,
		MapLong:             req.MapLong,
	}
	err = uc.locationRepo.CreateLocation(ctx, tX.DB(), loc)
	if err != nil {
		return nil, err
	}

	_, err = uc.storageUsecase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_LOCATION,
		SourceReferenceID: loc.ID,
		TargetReferenceID: "",
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: loc.ID,
		Data:        nil,
	}, nil
}

func (uc *LocationUseCase) DeleteteLocation(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	location, err := uc.locationRepo.GetLocation(ctx, uc.DB.DB(), models.LocationCondition{
		Where: models.LocationWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	// VALIDATE IF LOCATION IS IN USE
	totalAssets, _, err := uc.assetRepo.GetAssetManagementList(ctx, uc.DB.DB(), models.GetAssetListParam{
		ListRequest: commonmodel.ListRequest{
			PageNo:   1,
			PageSize: 1,
		},
		Cond: models.AssetCondition{
			Where: models.AssetWhere{
				ClientID:   claim.GetLoggedInClientID(),
				LocationID: location.ID,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	if totalAssets > 0 {
		err = errorhandler.ErrBadRequest("LOCATION_IS_IN_USE")
		return nil, err
	}

	if location.StatusCode == constants.LOCATION_STATUS_CODE_DELETED {
		return &commonmodel.DeleteResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: id,
		}, nil
	}

	updateLocation := models.Location{
		StatusCode: constants.LOCATION_STATUS_CODE_DELETED,
	}
	err = uc.locationRepo.UpdateLocation(ctx, uc.DB.DB(), id, &updateLocation)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *LocationUseCase) UpdateLocation(ctx context.Context, id string, req dtos.UpdateLocation) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	location, err := uc.locationRepo.GetLocation(ctx, tx.DB(), models.LocationCondition{
		Where: models.LocationWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	isAnyUpdate, updateLocation, err := uc.checkUpdateLocation(ctx, claim.GetLoggedInClientID(), location, req)
	if err != nil {
		return nil, err
	}
	if isAnyUpdate {
		err = uc.locationRepo.UpdateLocation(ctx, tx.DB(), id, &updateLocation)
		if err != nil {
			return nil, err
		}
	}

	_, err = uc.storageUsecase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_LOCATION,
		SourceReferenceID: id,
		TargetReferenceID: "",
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil

}

func (uc *LocationUseCase) checkUpdateLocation(
	ctx context.Context, clientID string, currentLoc *models.Location, req dtos.UpdateLocation,
) (bool, models.Location, error) {
	updateLocation := models.Location{}
	isAnyUpdate := false
	if currentLoc.Name != req.Name {
		updateLocation.Name = req.Name
		isAnyUpdate = true
	}

	if currentLoc.IsInventoryLocation.Bool != req.IsInventoryLocation {
		updateLocation.IsInventoryLocation = sqlhelpers.NullBool(req.IsInventoryLocation)
		isAnyUpdate = true
	}

	if currentLoc.Address != req.Address {
		updateLocation.Address = req.Address
		isAnyUpdate = true
	}

	if currentLoc.Floor.String != req.Floor {
		updateLocation.Floor = sqlhelpers.NullString(req.Floor)
		isAnyUpdate = true
	}

	if currentLoc.Unit.String != req.Unit {
		updateLocation.Unit = sqlhelpers.NullString(req.Unit)
		isAnyUpdate = true
	}

	if currentLoc.StatusCode != req.StatusCode {
		updateLocation.StatusCode = req.StatusCode
		isAnyUpdate = true
	}

	if currentLoc.Description.String != req.Description {
		updateLocation.Description = sqlhelpers.NullString(req.Description)
		isAnyUpdate = true
	}

	if currentLoc.PICUserID != req.PICUserID {
		_, err := uc.userRepo.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
			Where: userIdentityModel.UserWhere{
				ID:       req.PICUserID,
				ClientID: clientID,
			},
		})
		if err != nil {
			return false, updateLocation, err
		}

		updateLocation.PICUserID = req.PICUserID
		isAnyUpdate = true
	}

	if currentLoc.MapLat != req.MapLat {
		updateLocation.MapLat = req.MapLat
		isAnyUpdate = true
	}

	if currentLoc.MapLong != req.MapLong {
		updateLocation.MapLong = req.MapLong
		isAnyUpdate = true
	}

	return isAnyUpdate, updateLocation, nil
}
