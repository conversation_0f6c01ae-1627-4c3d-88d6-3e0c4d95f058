package usecase

import (
	approvalRepo "assetfindr/internal/app/approval/repository"
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	financeConstants "assetfindr/internal/app/finance/constants"
	financeDtos "assetfindr/internal/app/finance/dtos"
	financeUsecase "assetfindr/internal/app/finance/usecase"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	uploadConstants "assetfindr/internal/app/upload/constants"
	uploadDto "assetfindr/internal/app/upload/dtos"
	uploadModel "assetfindr/internal/app/upload/models"
	uploadRepo "assetfindr/internal/app/upload/repository"
	userIdentityModels "assetfindr/internal/app/user-identity/models"
	userIdentityRepo "assetfindr/internal/app/user-identity/repository"
	internalConstants "assetfindr/internal/constants"
	"bufio"
	"bytes"
	"html/template"
	"io"
	"os"

	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/timehelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/SebastiaanKlippert/go-wkhtmltopdf"
	"github.com/gocarina/gocsv"
	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type AssetTyreUseCase struct {
	DB                            database.DBUsecase
	AssetTyreRepository           repository.AssetTyreRepository
	AssetRepository               repository.AssetRepository
	AssetAssignmentUseCase        *AssetAssignmentUseCase
	AssetLinkedUseCase            AssetLinkedUseCase
	AssetLogRepo                  repository.AssetLogRepository
	AssetAssignmentRepo           repository.AssetAssignmentRepository
	notifUseCase                  notificationUsecase.NotificationUseCase
	AssetLinkedRepo               repository.AssetLinkedRepository
	financeUseCase                financeUsecase.FinanceUseCase
	attachmentUsecase             storageUsecase.AttachmentUseCase
	userIdentityRepo              userIdentityRepo.UserRepository
	storageUseCase                *storageUsecase.StorageUseCase
	uploadRepo                    uploadRepo.UploadRepository
	vehicleBodyTypeRepo           repository.AssetVehicleBodyTypeRepository
	brandRepo                     repository.BrandRepository
	AssetInspectionTyreRepository repository.AssetInspectionTyreRepository
	partnerRepo                   userIdentityRepo.PartnerRepository
	AssetUseCase                  AssetUseCase
	approvalRepo                  approvalRepo.ApprovalRepository
}

func NewAssetTyreUseCase(
	DB database.DBUsecase,
	assetTyreRepository repository.AssetTyreRepository,
	assetRepository repository.AssetRepository,
	AssetLogRepo repository.AssetLogRepository,
	AssetAssignmentRepo repository.AssetAssignmentRepository,
	assetLinkedRepo repository.AssetLinkedRepository,
	financeUseCase financeUsecase.FinanceUseCase,
	attachmentUsecase storageUsecase.AttachmentUseCase,
	userIdentityRepo userIdentityRepo.UserRepository,
	vehicleBodyTypeRepo repository.AssetVehicleBodyTypeRepository,
	brandRepo repository.BrandRepository,
	uploadRepo uploadRepo.UploadRepository,
	storageUseCase *storageUsecase.StorageUseCase,
	AssetInspectionTyreRepository repository.AssetInspectionTyreRepository,
	partnerRepo userIdentityRepo.PartnerRepository,
	approvalRepo approvalRepo.ApprovalRepository,
) *AssetTyreUseCase {
	return &AssetTyreUseCase{
		DB:                            DB,
		AssetTyreRepository:           assetTyreRepository,
		AssetRepository:               assetRepository,
		AssetAssignmentUseCase:        nil,
		AssetLogRepo:                  AssetLogRepo,
		AssetAssignmentRepo:           AssetAssignmentRepo,
		AssetLinkedRepo:               assetLinkedRepo,
		financeUseCase:                financeUseCase,
		attachmentUsecase:             attachmentUsecase,
		userIdentityRepo:              userIdentityRepo,
		vehicleBodyTypeRepo:           vehicleBodyTypeRepo,
		brandRepo:                     brandRepo,
		uploadRepo:                    uploadRepo,
		storageUseCase:                storageUseCase,
		AssetInspectionTyreRepository: AssetInspectionTyreRepository,
		partnerRepo:                   partnerRepo,
		approvalRepo:                  approvalRepo,
	}
}

func (uc *AssetTyreUseCase) SetNotifUseCase(notifUseCase notificationUsecase.NotificationUseCase) {
	uc.notifUseCase = notifUseCase
}

func (uc *AssetTyreUseCase) UpdateAssetAssignmentUseCase(assetAssignmentUseCase *AssetAssignmentUseCase) {
	uc.AssetAssignmentUseCase = assetAssignmentUseCase
}

func (uc *AssetTyreUseCase) UpdateAssetLinkedUseCase(assetLinkedUseCase AssetLinkedUseCase) {
	uc.AssetLinkedUseCase = assetLinkedUseCase
}

func (uc *AssetTyreUseCase) UpdateAssetUseCase(assetUseCase AssetUseCase) {
	uc.AssetUseCase = assetUseCase
}

func (uc *AssetTyreUseCase) GetAssetTyreList(ctx context.Context, req dtos.GetAssetTyresRequest) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyres, err := uc.AssetTyreRepository.GetAssetTyreListV2(
		ctx, uc.DB.DB(),
		models.GetAssetTyreListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyreCondition{
				Where: models.AssetTyreWhere{
					ClientID:                  claim.GetLoggedInClientID(),
					StatusCodes:               req.StatusCodes,
					BrandIDs:                  req.BrandIDs,
					AssignedUserIds:           req.AssignedUserIDs,
					TyreSizes:                 req.TyreSizes,
					HasPartnerOwnerID:         req.HasPartnerOwnerID,
					PartnerOwnerID:            req.PartnerOwnerID,
					Rfid:                      req.Rfids,
					MeterCalculationCodeOrNil: req.MeterCalculationCode,
					IsWorkshop:                req.IsWorkshop,

					UtilizationRateStatusCodes: req.TURStatusCodes,
					LastInspectedBeforeDate:    req.LastInspectedBeforeDate,
					IsCriticalOnLinkedNonSpare: req.IsCriticalOnLinkedNonSpare,

					InitialConditionCode: req.InitialConditionCode,
				},
				Preload: models.AssetTyrePreload{
					Tyre:               true,
					Asset:              true,
					AssetBrand:         true,
					AssetStatus:        true,
					AssetLinkedVehicle: false,
					RetreadTyre:        true,
				},
			},
			IsUnlinkTyre: req.IsUnlinkTyre,
		},
	)
	if err != nil {
		return nil, err
	}

	if len(assetTyres) == 0 {
		return &commonmodel.ListResponse{
			TotalRecords: totalRecords,
			PageSize:     req.ListRequest.PageSize,
			PageNo:       req.ListRequest.PageNo,
			Data:         nil,
		}, nil
	}

	assetIDs := make([]string, 0, len(assetTyres))
	for i := range assetTyres {
		assetIDs = append(assetIDs, assetTyres[i].AssetID)
	}

	assetTyrePositions, err := uc.AssetLinkedRepo.GetAssetTyrePosition(ctx, uc.DB.DB(), assetIDs)
	if err != nil {
		return nil, err
	}

	parentAssetIDs := make([]string, 0, len(assetTyrePositions))
	for i := range assetTyrePositions {
		parentAssetIDs = append(parentAssetIDs, assetTyrePositions[i].ParentAssetID)
	}

	mapAssetIDToTyrePosition := map[string]dtos.AssetTyrePosition{}
	for i := range assetTyrePositions {
		mapAssetIDToTyrePosition[assetTyrePositions[i].ChildAssetID] = assetTyrePositions[i]
	}

	mapParentAsset := map[string]models.Asset{}
	userIDs := make([]string, 0, len(assetTyres))
	if len(parentAssetIDs) > 0 {
		parentAssets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), models.AssetCondition{
			Where: models.AssetWhere{
				IDs: parentAssetIDs,
			},
			Preload: models.AssetPreload{
				AssetAssignment: true,
			},
		})
		if err != nil {
			return nil, err
		}
		for i := range parentAssets {
			mapParentAsset[parentAssets[i].ID] = parentAssets[i]

			if parentAssets[i].AssetAssignment == nil {
				continue
			}

			userIDs = append(userIDs, parentAssets[i].AssetAssignment.UserID)
		}
	}

	mapUserName := map[string]string{}
	if len(userIDs) > 0 {
		users, err := uc.userIdentityRepo.GetUsersV2(ctx, uc.DB.DB(), userIdentityModels.UserCondition{
			Where: userIdentityModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}
		for i := range users {
			mapUserName[users[i].ID] = users[i].GetName()
		}
	}

	respData := make([]dtos.AssetTyreListItemResp, 0, len(assetTyres))
	for _, assetTyre := range assetTyres {
		assingedToUserID := ""
		assingedToUserName := ""
		tyrePosition := mapAssetIDToTyrePosition[assetTyre.AssetID]
		parentAsset := mapParentAsset[tyrePosition.ParentAssetID]
		if parentAsset.AssetAssignment != nil {
			assingedToUserID = parentAsset.AssetAssignment.UserID
			assingedToUserName = mapUserName[parentAsset.AssetAssignment.UserID]
		}

		partnerOwnerName := assetTyre.Asset.Name
		if assetTyre.Asset.PartnerOwnerName != "" {
			partnerOwnerName = assetTyre.Asset.PartnerOwnerName
		}

		assetTyreSumLifeTimeKM := assetTyre.SumLifeTimeKM()
		assetTyreSumLifeTimeHM := assetTyre.SumLifeTimeHM()
		projectedLifeKM := assetTyre.LifetimeProjectedLifeKM()
		projectedLifeHm := assetTyre.LifetimeProjectedLifeHm()

		respData = append(respData, dtos.AssetTyreListItemResp{
			ID:                                    assetTyre.AssetID,
			Name:                                  assetTyre.Asset.Name,
			SerialNumber:                          assetTyre.Asset.SerialNumber,
			AssetStatusCode:                       assetTyre.Asset.AssetStatusCode,
			AssetStatus:                           commonmodel.ConstantModel{Code: assetTyre.Asset.AssetStatus.Code, Label: assetTyre.Asset.AssetStatus.Label, Description: assetTyre.Asset.AssetStatus.Description},
			PartnerOwnerID:                        assetTyre.Asset.PartnerOwnerID,
			PartnerOwnerNo:                        assetTyre.Asset.PartnerOwnerID,
			PartnerOwnerName:                      partnerOwnerName,
			BrandID:                               assetTyre.Asset.BrandID,
			BrandName:                             assetTyre.Asset.Brand.BrandName,
			DateCode:                              assetTyre.DateCode.String,
			DOTCode:                               assetTyre.DOTCode.String,
			TyreID:                                assetTyre.TyreID,
			Tyre:                                  dtos.Tyre{ID: assetTyre.Tyre.ID, CreatedAt: assetTyre.Tyre.CreatedAt, UpdatedAt: assetTyre.Tyre.UpdatedAt, BrandID: assetTyre.Tyre.BrandID, PatternType: assetTyre.Tyre.PatternType, OriginalTd: assetTyre.Tyre.OriginalTd, ConstructionType: assetTyre.Tyre.ConstructionType, PlyRating: assetTyre.Tyre.PlyRating, LoadRating: assetTyre.Tyre.LoadRating, SpeedIndex: assetTyre.Tyre.SpeedIndex, StarRating: assetTyre.Tyre.StarRating, TraCode: assetTyre.Tyre.TRACode, ClientID: assetTyre.Tyre.ClientID, SectionWidth: assetTyre.Tyre.SectionWidth, Construction: assetTyre.Tyre.Construction, RimDiameter: assetTyre.Tyre.RimDiameter},
			TyrePosition:                          tyrePosition.TyrePosition,
			AttachedAssetWithAssetID:              tyrePosition.ParentAssetID,
			AttachedAssetWithAssetReferenceNumber: parentAsset.ReferenceNumber,
			AttachedAssetWithAssetSerialNumber:    parentAsset.SerialNumber,
			AssingedToUserID:                      assingedToUserID,
			AssingedToUserName:                    assingedToUserName,
			DatetimeOfLastCheck:                   assetTyre.LastInspectedAt,
			LastInspectedAt:                       assetTyre.LastInspectedAt,
			AverageRTD:                            assetTyre.AverageRTD,
			TotalKM:                               assetTyre.TotalKM,
			TotalHm:                               calculationhelpers.Div100(assetTyre.TotalHm),
			LifetimeKM:                            assetTyreSumLifeTimeKM,
			LifetimeHm:                            assetTyreSumLifeTimeHM,
			TUR:                                   assetTyre.UtilizationRatePercentage,
			UtilizationRateStatusCode:             assetTyre.UtilizationRatePercentageStatusCode,
			Rfid:                                  assetTyre.Asset.Rfid,
			ProjectedLifeKM:                       projectedLifeKM,
			ProjectedLifeHm:                       projectedLifeHm,
			MeterCalculationCode:                  assetTyre.MeterCalculationCode,
			TreadNumber:                           assetTyre.RetreadNumber,
			StartThreadDepth:                      assetTyre.StartThreadDepth,
			OriginalTd:                            assetTyre.OriginalTd,
			IsWorkshop:                            assetTyre.Asset.IsWorkshop,
			HasNotSetRTD:                          assetTyre.HasNotSetRTD,
			InitialConditionCode:                  assetTyre.Asset.InitialConditionCode,
			IsSpare:                               assetTyre.IsSpare,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.ListRequest.PageSize,
		PageNo:       req.ListRequest.PageNo,
		Data:         respData,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreByIDV2(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	assetTyre, err := uc.AssetTyreRepository.GetAssetTyre(ctx, uc.DB.DB(), models.AssetTyreCondition{
		Where: models.AssetTyreWhere{
			ClientID: claim.GetLoggedInClientID(),
			AssetID:  id,
		},
		Preload: models.AssetTyrePreload{
			Tyre:                   true,
			Asset:                  true,
			AssetBrand:             true,
			AssetLinkedVehicle:     true,
			RetreadTyre:            true,
			RetreadTyreType:        true,
			RetreadTyreTreadConfig: true,
		},
	})
	if err != nil {
		return nil, err
	}

	_, err = uc.AssetLinkedRepo.GetAssetTyrePositionById(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	partnerIDs := []string{}
	for i := range assetTyre.RetreadTyre {
		if assetTyre.RetreadTyre[i].PartnerID != "" {
			partnerIDs = append(partnerIDs, assetTyre.RetreadTyre[i].PartnerID)
		}
	}

	mapPartners := map[string]userIdentityModels.Partner{}
	if len(partnerIDs) > 0 {
		partners, err := uc.partnerRepo.GetPartners(ctx, uc.DB.DB(), userIdentityModels.PartnerCondition{
			Where: userIdentityModels.PartnerWhere{
				IDs: partnerIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range partners {
			mapPartners[partners[i].ID] = partners[i]
		}
	}

	journalRefIDs := make([]string, 0, len(assetTyre.RetreadTyre)+1)
	journalRefIDs = append(journalRefIDs, id)
	lifetimeKm := 0
	var lifetimeHm int
	lifetimeActive := 0
	for _, tread := range assetTyre.RetreadTyre {
		journalRefIDs = append(journalRefIDs, tread.ID)
		lifetimeKm += tread.TotalKM
		lifetimeHm += tread.TotalHm
		lifetimeActive += tread.TotalLifetime
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefIDs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	retreads := []dtos.RetreadAssetTyreRes{}
	treadZeroCost := 0
	var lifetimeRunningCost float64 = 0
	for i, val := range assetTyre.RetreadTyre {
		isLastIdx := i == len(assetTyre.RetreadTyre)-1
		tread := dtos.BuildRetreadAssetTyreRes(assetTyre, val, mapRefAmounts[val.ID], mapPartners[val.PartnerID].Name, isLastIdx)
		retreads = append(retreads, tread)
		if i == 0 {
			treadZeroCost = tread.Cost
		}

		lifetimeRunningCost += tread.RunningCost
	}

	var lifetimeCPK float64 = 0
	if lifeKm := lifetimeKm - int(assetTyre.PrevTotalKM.Int64); lifeKm > 0 {
		lifetimeCPK = float64(lifetimeRunningCost) / float64(lifeKm)
	}

	var lifetimeCPH float64 = 0
	if lifeHm := lifetimeHm - int(assetTyre.PrevTotalHm.Int64); lifeHm > 0 {
		lifetimeCPH = float64(lifetimeRunningCost) / calculationhelpers.Div100(lifeHm)
	}

	prevTotalHm := null.Float{}
	if assetTyre.PrevTotalHm.Valid {
		prevTotalHm = null.FloatFrom(calculationhelpers.Div100(int(assetTyre.PrevTotalHm.Int64)))
	}

	resp := dtos.AssetTyreDetail{
		Cost:        mapRefAmounts[assetTyre.AssetID].Amount,
		CostAsset:   treadZeroCost,
		CostExpense: mapRefAmounts[assetTyre.AssetID].AmountExpense,
		CreatedAt:   assetTyre.CreatedAt,
		UpdatedAt:   assetTyre.UpdatedAt,
		AssetID:     assetTyre.AssetID,

		Asset:                           *dtos.BuildAssetResp(&assetTyre.Asset),
		DatetimeOfLastCheck:             assetTyre.DatetimeOfLastCheck,
		AverageRTD:                      assetTyre.AverageRTD,
		UtilizationRatePercentage:       assetTyre.UtilizationRatePercentage,
		TotalKm:                         assetTyre.TotalKM,
		TotalHm:                         calculationhelpers.Div100(assetTyre.TotalHm),
		ProjectedLifeKM:                 assetTyre.LifetimeProjectedLifeKM(),
		ProjectedLifeHm:                 assetTyre.LifetimeProjectedLifeHm(),
		RetreadNumber:                   assetTyre.RetreadNumber,
		RepairedNumber:                  assetTyre.RepairedNumber,
		DotCode:                         assetTyre.DOTCode.String,
		DateCode:                        assetTyre.DateCode.String,
		ClientID:                        assetTyre.ClientID,
		TyreID:                          assetTyre.TyreID,
		StartThreadDepth:                assetTyre.StartThreadDepth,
		OriginalTd:                      assetTyre.OriginalTd,
		MeterCalculationCode:            assetTyre.MeterCalculationCode,
		AverageRtdLastUpdatedAt:         assetTyre.AverageRtdLastUpdatedAt,
		Pressure:                        assetTyre.Pressure,
		PressureLastUpdatedAt:           assetTyre.PressureLastUpdatedAt,
		Temperature:                     assetTyre.Temperature,
		TemperatureLastUpdatedAt:        assetTyre.TemperatureLastUpdatedAt,
		PressureLastUpdatedSensorRef:    assetTyre.PressureLastUpdatedSensorRef,
		TemperatureLastUpdatedSensorRef: assetTyre.TemperatureLastUpdatedSensorRef,
		TotalLifetime:                   lifetimeActive,
		LifetimeKM:                      lifetimeKm,
		LifetimeCPK:                     lifetimeCPK,
		LifetimeCPH:                     lifetimeCPH,
		LifetimeHM:                      calculationhelpers.Div100(lifetimeHm),
		PartnerOwnerID:                  assetTyre.Asset.PartnerOwnerID,
		PartnerOwnerNo:                  assetTyre.Asset.PartnerOwnerNo,
		PartnerOwnerName:                assetTyre.Asset.PartnerOwnerName,
		Tyre:                            dtos.BuildTyreResp(assetTyre.Tyre),
		RetreadTyre:                     retreads,
		HasNotSetRTD:                    assetTyre.HasNotSetRTD,
		IsWorkshop:                      assetTyre.Asset.IsWorkshop,
		PrevKmHmDataUnavailable:         assetTyre.PrevKmHmDataUnavailable,
		PrevTotalKM:                     assetTyre.PrevTotalKM,
		PrevTotalHm:                     prevTotalHm,
		IsSpare:                         assetTyre.IsSpare,
	}

	if assetTyre.AssetLinked != nil {
		tyrePosition := 0
		if assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre != nil {
			tyrePosition = assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre.TyrePosition
		}

		resp.AssetVehicleLinked = &dtos.AssetVehicleLinked{
			ID:                 assetTyre.AssetLinked.ParentAssetID,
			RegistrationNumber: assetTyre.AssetLinked.ParentAsset.RegistrationNumber,
			SerialNumber:       assetTyre.AssetLinked.ParentAsset.Asset.SerialNumber,
			ReferenceNumber:    assetTyre.AssetLinked.ParentAsset.Asset.ReferenceNumber,
			VehicleBodyType: dtos.VehicleBodyType{
				Code:        assetTyre.AssetLinked.ParentAsset.AssetVehicleBodyType.ID,
				Label:       assetTyre.AssetLinked.ParentAsset.AssetVehicleBodyType.Label,
				Description: assetTyre.AssetLinked.ParentAsset.AssetVehicleBodyType.Description,
			},
			Name:           assetTyre.AssetLinked.ParentAsset.Asset.Name,
			LinkedDatetime: assetTyre.AssetLinked.LinkedDatetime,
			TyrePosition:   tyrePosition,
		}

	}

	inspectionTyre, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyre(ctx, uc.DB.DB(), models.AssetInspectionTyreCondition{
		Where: models.AssetInspectionTyreWhere{
			AssetID: id,
		},
		Preload: models.AssetInspectionTyrePreload{
			AssetInspection: true,
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if err == nil {
		assetInspectionInspectedByUserName := ""
		user, err := uc.userIdentityRepo.GetUser(ctx, uc.DB.DB(), userIdentityModels.UserCondition{
			Where: userIdentityModels.UserWhere{
				ID: inspectionTyre.AssetInspection.InspectByUserID,
			},
		})
		if err == nil {
			assetInspectionInspectedByUserName = user.GetName()
		}
		resp.LastInspectionTyre = dtos.BuildAnAssetInspectionTyresResponse(*inspectionTyre, assetInspectionInspectedByUserName)
	}

	latestInspectionTyreRTD, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyre(ctx, uc.DB.DB(), models.AssetInspectionTyreCondition{
		Where:   models.AssetInspectionTyreWhere{AssetID: id, HasRTD: true},
		Preload: models.AssetInspectionTyrePreload{AssetInspection: true},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if err == nil {
		assetInspectionInspectedByUserName := ""
		user, err := uc.userIdentityRepo.GetUser(ctx, uc.DB.DB(), userIdentityModels.UserCondition{
			Where: userIdentityModels.UserWhere{
				ID: inspectionTyre.AssetInspection.InspectByUserID,
			},
		})
		if err == nil {
			assetInspectionInspectedByUserName = user.GetName()
		}
		resp.LastInspectionTyreTreadDepth = dtos.BuildAnAssetInspectionTyresResponse(*latestInspectionTyreRTD, assetInspectionInspectedByUserName)
	}

	latestInspectionTyrePressure, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyre(ctx, uc.DB.DB(), models.AssetInspectionTyreCondition{
		Where:   models.AssetInspectionTyreWhere{AssetID: id, HasPressure: true},
		Preload: models.AssetInspectionTyrePreload{AssetInspection: true},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if err == nil {
		assetInspectionInspectedByUserName := ""
		user, err := uc.userIdentityRepo.GetUser(ctx, uc.DB.DB(), userIdentityModels.UserCondition{
			Where: userIdentityModels.UserWhere{
				ID: inspectionTyre.AssetInspection.InspectByUserID,
			},
		})
		if err == nil {
			assetInspectionInspectedByUserName = user.GetName()
		}
		resp.LastInspectionTyrePressure = dtos.BuildAnAssetInspectionTyresResponse(*latestInspectionTyrePressure, assetInspectionInspectedByUserName)
	}

	latestInspectionTyreTemperature, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyre(ctx, uc.DB.DB(), models.AssetInspectionTyreCondition{
		Where:   models.AssetInspectionTyreWhere{AssetID: id, HasTemperature: true},
		Preload: models.AssetInspectionTyrePreload{AssetInspection: true},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if err == nil {
		assetInspectionInspectedByUserName := ""
		user, err := uc.userIdentityRepo.GetUser(ctx, uc.DB.DB(), userIdentityModels.UserCondition{
			Where: userIdentityModels.UserWhere{
				ID: inspectionTyre.AssetInspection.InspectByUserID,
			},
		})
		if err == nil {
			assetInspectionInspectedByUserName = user.GetName()
		}
		resp.LastInspectionTyreTemperature = dtos.BuildAnAssetInspectionTyresResponse(*latestInspectionTyreTemperature, assetInspectionInspectedByUserName)
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        resp,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyreByID(ctx context.Context, id string) (*models.AssetTyre, int, float64, error) {
	// claim, err := authhelpers.GetClaimFromCtx(ctx)
	// if err != nil {
	// 	return nil, 0, err
	// }

	assetTyre, err := uc.AssetTyreRepository.GetAssetTyre(ctx, uc.DB.DB(), models.AssetTyreCondition{
		Where: models.AssetTyreWhere{
			// ClientID: claim.GetLoggedInClientID(),
			AssetID: id,
		},
		Preload: models.AssetTyrePreload{
			Tyre:       true,
			Asset:      true,
			AssetBrand: true,
			// AssetLinkedVehicle: true,
		},
	})
	if err != nil {
		return nil, 0, 0, err
	}

	assetTyrePosition, err := uc.AssetLinkedRepo.GetAssetTyrePositionById(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, 0, 0, err
	}
	tUR := assetTyre.UtilizationRatePercentage
	return assetTyre, assetTyrePosition, tUR, nil
}

var ErrAssetTyreNotFound = errors.New("AssetTyre not found")

func (uc *AssetTyreUseCase) ValidateCreateAssetTyre(ctx context.Context, createDTO *dtos.AssetTyreItemReceiver) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	if exists, err := uc.AssetRepository.IsSerialNumberExist(ctx, uc.DB.DB(), createDTO.SerialNumber, claim.GetLoggedInClientID()); err != nil {
		return err
	} else if exists {
		return errors.New("serial number already exists")
	}

	return nil
}

func (uc *AssetTyreUseCase) CreateAssetTyre(ctx context.Context, createDTO *dtos.AssetTyreItemReceiver) (*models.Asset, error) {
	createDTO.StatusCode = constants.ASSET_STATUS_CODE_NEW_STOCK

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	asset := &models.Asset{
		Name:              createDTO.Name,
		BrandID:           createDTO.BrandID,
		SerialNumber:      createDTO.SerialNumber,
		AssetCategoryCode: constants.ASSET_CATEGORY_TYRE_CODE,
		SubCategoryCode:   constants.ASSET_SUB_CATEGORY_TYRE_CODE,
		AssetStatusCode:   createDTO.StatusCode,
		Cost:              createDTO.Cost,
		Rfid:              createDTO.Rfid,
		ModelV2: commonmodel.ModelV2{
			CreatedBy: claim.UserID,
			ClientID:  claim.GetLoggedInClientID(),
		},
		OwnershipCategoryCode: constants.ASSET_OWNERSHIP_OWN_CODE,
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	err = uc.AssetRepository.CreateAsset(ctx, tx.DB(), asset)
	if err != nil {
		return nil, err
	}

	assetTyre := &models.AssetTyre{
		AssetID:             asset.ID,
		DateCode:            null.StringFrom(createDTO.DateCode),
		DOTCode:             null.StringFrom(createDTO.DOTCode),
		DatetimeOfLastCheck: time.Now().In(time.UTC),
		AverageRTD:          createDTO.StartThreadDepth,
		TotalKM:             createDTO.TotalKM,
		TotalHm:             calculationhelpers.Multiply100(createDTO.TotalHm),
		TyreID:              createDTO.TyreID,
		StartThreadDepth:    createDTO.StartThreadDepth,
	}

	err = uc.AssetTyreRepository.CreateAssetTyre(ctx, tx.DB(), assetTyre)
	if err != nil {
		return nil, err
	}

	tread := models.AssetTyreTread{
		AssetID:          asset.ID,
		ThreadSequence:   assetTyre.RetreadNumber,
		AverageRTD:       assetTyre.AverageRTD,
		TotalKM:          assetTyre.TotalKM,
		TotalHm:          assetTyre.TotalHm,
		StartThreadDepth: assetTyre.StartThreadDepth,
		TotalLifetime:    assetTyre.TotalLifetime,
		VendorName:       null.String{},
		BrandName:        "",
		RetreadType:      "",
		Cost:             int(asset.Cost),
	}

	err = uc.AssetTyreRepository.CreateAssetTyreTread(ctx, tx.DB(), &tread)
	if err != nil {
		return nil, err
	}

	err = uc.financeUseCase.CreateJournal(ctx, financeDtos.CreateJournalReq{
		AccountTransactionType: financeConstants.ACCOUNT_TRANSACTION_TYPE_CREATE_TYRE_COST,
		Date:                   asset.CreatedAt,
		References: []financeDtos.JournalReference{
			{
				SourceCode:  financeConstants.JOURNAL_SOURCE_ASSET_CODE,
				ReferenceID: asset.ID,
			},
			// Add Cost For Retread 0
			{
				SourceCode:  financeConstants.JOURNAL_SOURCE_RETREAD_TYRE_CODE,
				ReferenceID: tread.ID,
			},
		},
		Notes:    "",
		Amount:   int64(asset.Cost),
		ClientID: claim.GetLoggedInClientID(),
		UserID:   claim.UserID,
	})
	if err != nil {
		return nil, err
	}

	_, err = uc.attachmentUsecase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET,
		SourceReferenceID: asset.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            createDTO.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return asset, nil
}

func (uc *AssetTyreUseCase) ValidateCreateAssetTyreV2(ctx context.Context, req dtos.UpdateAssetTyreReq) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	if exists, err := uc.AssetRepository.IsSerialNumberExist(ctx, uc.DB.DB(), req.SerialNumber, claim.GetLoggedInClientID()); err != nil {
		return err
	} else if exists {
		return errors.New("serial number already exists")
	}

	return nil
}

func (uc *AssetTyreUseCase) ValidateCreateAssetTyreSpecifications(ctx context.Context, req dtos.BulkAssetTyreReq) error {
	serialNumberSeen := make(map[string]bool)
	rfidSeen := make(map[string]bool)
	hasDuplicateInput := false

	parentAssetIdAndPositionSeen := make(map[string]map[int]bool)
	linkedTyreHasNoDuplicateTyrePositionAtSameTyre := false
	linkedTyreHasNoTyrePosition := false

	for _, tyreSpecification := range req.TyreSpecifications {
		if serialNumberSeen[tyreSpecification.SerialNumber] || rfidSeen[tyreSpecification.Rfid] {
			hasDuplicateInput = true
		}

		if tyreSpecification.SerialNumber != "" {
			serialNumberSeen[tyreSpecification.SerialNumber] = true
		}
		if tyreSpecification.Rfid != "" {
			rfidSeen[tyreSpecification.Rfid] = true
		}

		if tyreSpecification.ParentAssetID != "" && tyreSpecification.TyrePosition == 0 {
			linkedTyreHasNoTyrePosition = true
		}
		if tyreSpecification.ParentAssetID != "" && tyreSpecification.TyrePosition > 0 {
			if _, ok := parentAssetIdAndPositionSeen[tyreSpecification.ParentAssetID]; !ok {
				parentAssetIdAndPositionSeen[tyreSpecification.ParentAssetID] = make(map[int]bool)
			}
			if parentAssetIdAndPositionSeen[tyreSpecification.ParentAssetID][tyreSpecification.TyrePosition] {
				linkedTyreHasNoDuplicateTyrePositionAtSameTyre = true
			} else {
				parentAssetIdAndPositionSeen[tyreSpecification.ParentAssetID][tyreSpecification.TyrePosition] = true
			}
		}

	}

	if hasDuplicateInput {
		return errors.New("Serial number or rfid input has duplicate value!")
	}

	if linkedTyreHasNoTyrePosition {
		return errors.New("Linked tyre has invalid tyre position!")
	}

	if linkedTyreHasNoDuplicateTyrePositionAtSameTyre {
		return errors.New("Tyre position has duplicate!")
	}

	return nil
}

func (uc *AssetTyreUseCase) CreateAssetTyreV2(ctx context.Context, req dtos.BulkAssetTyreReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	var assetIDs []string
	var serialNumbers []string
	var rfids []string
	parentAssetIdAndPositions := make(map[string]map[int]bool)

	for _, tyreSpecification := range req.TyreSpecifications {
		if tyreSpecification.SerialNumber != "" {
			serialNumbers = append(serialNumbers, tyreSpecification.SerialNumber)
		}

		if tyreSpecification.Rfid != "" {
			rfids = append(rfids, tyreSpecification.Rfid)
		}

		if tyreSpecification.ParentAssetID != "" && tyreSpecification.TyrePosition > 0 {
			if _, ok := parentAssetIdAndPositions[tyreSpecification.ParentAssetID]; !ok {
				parentAssetIdAndPositions[tyreSpecification.ParentAssetID] = make(map[int]bool)
			}
			parentAssetIdAndPositions[tyreSpecification.ParentAssetID][tyreSpecification.TyrePosition] = true
		}
	}

	if len(serialNumbers) > 0 {
		serialNumbersChecklist, err := uc.AssetUseCase.SerialNumberIsExist(ctx, serialNumbers)
		if err != nil {
			return nil, err
		}
		for _, item := range serialNumbersChecklist.Data.(dtos.AssetSerialNumberResp).Result {
			if item.IsExist {
				return nil, errors.New("Serial number has existed before!")
			}
		}
	}

	if len(rfids) > 0 {
		rfidsChecklist, err := uc.AssetUseCase.RfidIsExists(ctx, rfids)
		if err != nil {
			return nil, err
		}
		for _, item := range rfidsChecklist.Data.(dtos.AssetRFIDRes).Result {
			if item.IsExist {
				return nil, errors.New("Rfid has existed before!")
			}
		}
	}

	if len(parentAssetIdAndPositions) > 0 {
		for parentAssetID, positionsMap := range parentAssetIdAndPositions {
			linkeds, err := uc.AssetLinkedUseCase.AssetLinkedRepository.GetAssetLinkeds(ctx, uc.DB.DB(), models.AssetLinkedCondition{
				Where: models.AssetLinkedWhere{
					ClientID:      claim.GetLoggedInClientID(),
					ParentAssetID: parentAssetID,
					WithUnlinked:  false,
					TypeCode:      constants.ASSET_LINKED_TYPE_VEHICLE_TYRE,
				},
				Preload: models.AssetLinkedPreload{
					AssetLinkedAssetVehicleTyre: true,
				},
			})
			if err != nil {
				return nil, err
			}

			for position := range positionsMap {
				for _, linked := range linkeds {
					if linked.AssetLinkedAssetVehicleTyre != nil && linked.AssetLinkedAssetVehicleTyre.TyrePosition == position {
						fmt.Printf("vehicle still has linked tyre in position %s\n", linked.ID)
						return nil, fmt.Errorf("vehicle still has linked tyre in position %d", position)
					}
				}
			}
		}
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	for _, tyreSpecification := range req.TyreSpecifications {
		asset := &models.Asset{
			Name:              req.Name,
			BrandID:           req.BrandID,
			SerialNumber:      tyreSpecification.SerialNumber,
			AssetCategoryCode: constants.ASSET_CATEGORY_TYRE_CODE,
			SubCategoryCode:   constants.ASSET_SUB_CATEGORY_TYRE_CODE,
			AssetStatusCode:   constants.ASSET_STATUS_CODE_NEW_STOCK,
			Rfid:              tyreSpecification.Rfid,
			ModelV2: commonmodel.ModelV2{
				CreatedBy: claim.UserID,
				ClientID:  claim.GetLoggedInClientID(),
			},
			OwnershipCategoryCode: constants.ASSET_OWNERSHIP_OWN_CODE,
			PartnerOwnerID:        req.PartnerOwnerID,
			PartnerOwnerNo:        req.PartnerOwnerNo,
			PartnerOwnerName:      req.PartnerOwnerName,
			InitialConditionCode:  req.InitialConditionCode,
			IsWorkshop:            req.IsWorkshop,
		}

		err = uc.AssetRepository.CreateAsset(ctx, tx.DB(), asset)
		if err != nil {
			return nil, err
		}

		prevTotalHm := null.Int{}
		if req.PrevTotalHm.Valid {
			prevTotalHm = null.IntFrom(int64(calculationhelpers.Multiply100(req.PrevTotalHm.Float64)))
		}

		assetTyre := &models.AssetTyre{
			AssetID:                 asset.ID,
			DateCode:                null.StringFrom(tyreSpecification.DateCode),
			DOTCode:                 null.StringFrom(tyreSpecification.DOTCode),
			DatetimeOfLastCheck:     time.Now().In(time.UTC),
			LastStockDate:           time.Now().In(time.UTC),
			AverageRTD:              req.AverageRTD,
			Rtd1:                    req.AverageRTD,
			Rtd2:                    req.AverageRTD,
			Rtd3:                    req.AverageRTD,
			Rtd4:                    req.AverageRTD,
			TotalKM:                 req.TotalKM,
			TotalHm:                 calculationhelpers.Multiply100(req.TotalHm),
			TyreID:                  req.TyreID,
			StartThreadDepth:        req.StartThreadDepth,
			OriginalTd:              req.OriginalTd,
			HasNotSetRTD:            req.HasNotSetRTD,
			PrevKmHmDataUnavailable: req.PrevKmHmDataUnavailable,
			PrevTotalKM:             req.PrevTotalKM,
			PrevTotalHm:             prevTotalHm,
		}

		err = uc.AssetTyreRepository.CreateAssetTyre(ctx, tx.DB(), assetTyre)
		if err != nil {
			return nil, err
		}

		tread := models.AssetTyreTread{
			AssetID:          asset.ID,
			ThreadSequence:   0,
			AverageRTD:       assetTyre.AverageRTD,
			TotalKM:          int(assetTyre.PrevTotalKM.Int64),
			TotalHm:          int(prevTotalHm.Int64),
			StartThreadDepth: assetTyre.StartThreadDepth,
			OriginalTd:       assetTyre.OriginalTd,
			TotalLifetime:    assetTyre.TotalLifetime,
			TypeCode:         constants.ASSET_TYRE_TREAD_TYPES_ORIGINAL,
			VendorName:       null.String{},
			BrandName:        "",
			RetreadType:      "",
			Cost:             0,
		}

		err = uc.AssetTyreRepository.CreateAssetTyreTread(ctx, tx.DB(), &tread)
		if err != nil {
			return nil, err
		}

		if tyreSpecification.ParentAssetID != "" && tyreSpecification.TyrePosition > 0 {
			createAssetLinkedResponse := &CreateAssetLinkedResponse{}
			err := uc.AssetLinkedUseCase.processLinkAssetTyre(ctx, tx, createAssetLinkedResponse, &dtos.AssetLinkedResponse{
				ParentAssetID:       tyreSpecification.ParentAssetID,
				ChildAssetID:        asset.ID,
				ClientID:            claim.GetLoggedInClientID(),
				LinkedAssetTypeCode: constants.ASSET_LINKED_TYPE_VEHICLE_TYRE,
				TyrePosition:        tyreSpecification.TyrePosition,
			},
			)
			if err != nil {
				return nil, err
			}
		}

		_, err = uc.attachmentUsecase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
			ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET,
			SourceReferenceID: asset.ID,
			ClientID:          claim.GetLoggedInClientID(),
			Photos:            req.Photos,
		})
		if err != nil {
			return nil, err
		}

		assetIDs = append(assetIDs, asset.ID)
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "Asset IDs",
		Data:        assetIDs,
	}, nil
}

func (uc *AssetTyreUseCase) UpdateAssetTyreV2(ctx context.Context, id string, req dtos.UpdateAssetTyreReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	existingAsset, err := uc.AssetRepository.GetAsset(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	if existingAsset.SerialNumber != req.SerialNumber {
		err = uc.ValidateCreateAssetTyreV2(ctx, req)
		if err != nil {
			return nil, err
		}
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()
	rfid := null.StringFrom(req.Rfid)
	serialNumber := null.StringFrom(req.SerialNumber)

	err = uc.AssetRepository.UpdateAssetForUpdateWithNull(ctx, tx.DB(), id, &models.AssetForUpdateWithNull{
		SerialNumber: serialNumber,
		Rfid:         rfid,
	})
	if err != nil {
		return nil, err
	}

	prevTotalHm := null.Int{}
	if req.PrevTotalHm.Valid {
		prevTotalHm = null.IntFrom(int64(calculationhelpers.Multiply100(req.PrevTotalHm.Float64)))
	}

	assetTyre := &models.AssetTyre{
		AssetID:     id,
		DateCode:    null.StringFrom(req.DateCode),
		DOTCode:     null.StringFrom(req.DOTCode),
		PrevTotalKM: req.PrevTotalKM,
		PrevTotalHm: prevTotalHm,
	}

	err = uc.AssetTyreRepository.UpdateAssetTyre(ctx, tx.DB(), assetTyre)
	if err != nil {
		return nil, err
	}

	_, err = uc.attachmentUsecase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET,
		SourceReferenceID: id,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *AssetTyreUseCase) GetTyres(ctx context.Context, tyres *[]models.Tyre, brand_id string) error {
	err := uc.AssetTyreRepository.GetTyres(ctx, uc.DB.DB(), tyres, brand_id)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AssetTyreUseCase) GetAssetTyresByIds(ctx context.Context, ids []string, req dtos.AssetTyreByIDsListReq) (commonmodel.ListResponse, error) {
	assetsResponse := commonmodel.ListResponse{}
	var assetTyres []models.AssetTyre

	parsedLinkedStartDate, _ := timehelpers.ParseDateFilter(req.LinkedStartDate, false)
	parsedLinkedEndDate, _ := timehelpers.ParseDateFilter(req.LinkedEndDate, true)
	err := uc.AssetTyreRepository.GetAssetTyresByIds(ctx, uc.DB.DB(), &assetTyres, ids, models.GetAssetTyreListParam{
		ListRequest: req.ListRequest,
		Cond: models.AssetTyreCondition{
			Where: models.AssetTyreWhere{
				BrandIDs:        req.BrandIDs,
				LinkedStartDate: parsedLinkedStartDate,
				LinkedEndDate:   parsedLinkedEndDate,
				PatternTypes:    req.PatternTypes,
				TyreSizes:       req.TyreSizes,
				TURStatusCodes:  req.TURStatusCodes,
				LinkedByUserIDs: req.LinkedByUserIDs,
			},
		},
	})
	if err != nil {
		return assetsResponse, err
	}

	// Get Asset Assignments
	var assetIds []string
	for _, asset := range assetTyres {
		assetIds = append(assetIds, asset.AssetID)
	}

	assignmentsMapByAssetId := map[string]dtos.AssetAssignmentResponse{}

	err = uc.AssetAssignmentUseCase.GetAssetAssignmentByAssetIds(ctx, &assignmentsMapByAssetId, assetIds)
	if err != nil {
		commonlogger.Errorf("Error in getting asset assignments by assignment ids", err)
		return assetsResponse, err
	}

	linkedAssetMapByChildAssetIds := map[string]models.AssetLinked{}

	err = uc.AssetLinkedUseCase.GetLinkedAssetMapByChildAssetIds(ctx, &linkedAssetMapByChildAssetIds, assetIds)
	if err != nil {
		commonlogger.Errorf("Error in getting linked asset by asset child ids", err)
		return assetsResponse, err
	}

	atp, err := uc.AssetLinkedRepo.GetAssetTyrePosition(ctx, uc.DB.DB(), assetIds)
	if err != nil {
		return assetsResponse, err
	}
	newResponse := dtos.BuildAssetTyreListResponse(assetTyres, assignmentsMapByAssetId, linkedAssetMapByChildAssetIds, atp)

	assetsResponse = commonmodel.ListResponse{
		TotalRecords: len(newResponse),
		PageSize:     len(newResponse),
		PageNo:       1,
		Data:         newResponse,
	}

	return assetsResponse, nil
}

func (uc *AssetTyreUseCase) GetAssetTyresByLinkedParentAssetId(ctx context.Context, parentAssetId string) (commonmodel.ListResponse, error) {
	assetsResponse := commonmodel.ListResponse{}
	var assetTyres []models.AssetTyre

	err := uc.AssetTyreRepository.GetAssetTyresByLinkedParentAssetId(ctx, uc.DB.DB(), &assetTyres, parentAssetId)
	if err != nil {
		return assetsResponse, err
	}

	// Get Asset Assignments
	var assetIds []string
	for _, asset := range assetTyres {
		assetIds = append(assetIds, asset.AssetID)
	}

	assignmentsMapByAssetId := map[string]dtos.AssetAssignmentResponse{}

	err = uc.AssetAssignmentUseCase.GetAssetAssignmentByAssetIds(ctx, &assignmentsMapByAssetId, assetIds)
	if err != nil {
		commonlogger.Errorf("Error in getting asset assignments by assignment ids", err)
		return assetsResponse, err
	}

	linkedAssetMapByChildAssetIds := map[string]models.AssetLinked{}

	err = uc.AssetLinkedUseCase.GetLinkedAssetMapByChildAssetIds(ctx, &linkedAssetMapByChildAssetIds, assetIds)
	if err != nil {
		commonlogger.Errorf("Error in getting linked asset by asset child ids", err)
		return assetsResponse, err
	}

	atp, err := uc.AssetLinkedRepo.GetAssetTyrePosition(ctx, uc.DB.DB(), assetIds)
	if err != nil {
		return assetsResponse, err
	}
	newResponse := dtos.BuildAssetTyreListResponse(assetTyres, assignmentsMapByAssetId, linkedAssetMapByChildAssetIds, atp)

	assetsResponse = commonmodel.ListResponse{
		TotalRecords: len(newResponse),
		PageSize:     len(newResponse),
		PageNo:       1,
		Data:         newResponse,
	}

	return assetsResponse, nil
}

func (uc *AssetTyreUseCase) UpdateAssetTyre(ctx context.Context, param dtos.UpdateAssetTyreParam) (*dtos.AssetTyreItemReceiver, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	assetTyre, err := uc.AssetTyreRepository.GetAssetTyreByID(ctx, uc.DB.DB(), param.AssetID)
	if err != nil {
		return nil, err
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	updatedAssetTyre := &models.AssetTyre{
		AssetID:             param.AssetID,
		DateCode:            null.StringFrom(param.Req.DateCode),
		DOTCode:             null.StringFrom(param.Req.DOTCode),
		DatetimeOfLastCheck: time.Now().In(time.UTC),
		AverageRTD:          param.Req.AverageRTD,
		TyreID:              param.Req.TyreID,
		StartThreadDepth:    param.Req.StartThreadDepth,
	}

	logMapPrev := map[string]interface{}{}
	logMapNew := map[string]interface{}{}
	isAnyAssetTyreUpdate := checkUpdateAssetTyre(assetTyre, updatedAssetTyre, logMapPrev, logMapNew)
	if isAnyAssetTyreUpdate {
		err = uc.AssetTyreRepository.UpdateAssetTyre(ctx, tX.DB(), updatedAssetTyre)
		if err != nil {
			commonlogger.Errorf("error while update asset tyre: %v\n", err)
			return nil, fmt.Errorf("error while update asset tyre")
		}
	}

	updatedAsset := &models.Asset{
		Name:            param.Req.Name,
		BrandID:         param.Req.BrandID,
		SerialNumber:    param.Req.SerialNumber,
		AssetStatusCode: param.Req.StatusCode,
		Cost:            param.Req.Cost,
		Rfid:            param.Req.Rfid,
	}
	isAnyAssetUpdate := checkUpdateAsset(&assetTyre.Asset, updatedAsset, logMapPrev, logMapNew)
	if isAnyAssetUpdate || isAnyAssetTyreUpdate {
		err = uc.AssetRepository.UpdateAsset(ctx, tX.DB(), updatedAsset)
		if err != nil {
			commonlogger.Errorf("error while update asset: %v\n", err)
			return nil, fmt.Errorf("error while update asset")
		}

		assetLog := models.AssetLog{
			UpdatedByUserID: claim.UserID,
			AssetID:         assetTyre.AssetID,
			PreviousValue:   pgtype.JSONB{},
			NewValue:        pgtype.JSONB{},
			ClientID:        assetTyre.ClientID,
			Type:            constants.ASSET_UPDATE_LOG_TYPE_UPDATE,
		}

		assetLog.SetUpdate(logMapPrev, logMapNew)

		err = uc.AssetLogRepo.CreateAssetLogs(ctx, tX.DB(), []models.AssetLog{assetLog})
		if err != nil {
			return nil, err
		}
		go uc.notifyAfterUpdateAssetTyre(contexthelpers.WithoutCancel(ctx), param.AssetID)
	}

	_, err = uc.attachmentUsecase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_ASSET,
		SourceReferenceID: param.AssetID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            param.Req.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		commonlogger.Errorf("Error register user when commit to database: %v\n", err)
		return nil, err
	}

	return &param.Req, nil
}

func checkUpdateAssetTyre(
	assetTyre *models.AssetTyre, updateAssetTyre *models.AssetTyre,
	logMapPrev map[string]interface{},
	logMapNew map[string]interface{},
) bool {
	isAnyUpdate := false
	updatedAssetTyre := &models.AssetTyre{}
	updatedAssetTyre.AssetID = assetTyre.AssetID

	if assetTyre.DateCode != updateAssetTyre.DateCode {
		isAnyUpdate = true
		updatedAssetTyre.DateCode = updateAssetTyre.DateCode
		field := "Date code"
		logMapPrev[field] = assetTyre.DateCode
		logMapNew[field] = updatedAssetTyre.DateCode
	}

	if assetTyre.DOTCode != updateAssetTyre.DOTCode {
		isAnyUpdate = true
		updatedAssetTyre.DOTCode = updateAssetTyre.DOTCode
		field := "DOT code"
		logMapPrev[field] = assetTyre.DOTCode
		logMapNew[field] = updatedAssetTyre.DOTCode
	}

	if assetTyre.AverageRTD != updateAssetTyre.AverageRTD {
		isAnyUpdate = true
		updatedAssetTyre.AverageRTD = updateAssetTyre.AverageRTD
		field := "Average RTD"
		logMapPrev[field] = assetTyre.AverageRTD
		logMapNew[field] = updatedAssetTyre.AverageRTD
	}

	if assetTyre.TyreID != updateAssetTyre.TyreID {
		isAnyUpdate = true
		updatedAssetTyre.TyreID = updateAssetTyre.TyreID
		field := "Tyre ID"
		logMapPrev[field] = assetTyre.TyreID
		logMapNew[field] = updatedAssetTyre.TyreID
	}

	if assetTyre.StartThreadDepth != updateAssetTyre.StartThreadDepth {
		isAnyUpdate = true
		updatedAssetTyre.StartThreadDepth = updateAssetTyre.StartThreadDepth
		field := "Start thread depth"
		logMapPrev[field] = assetTyre.StartThreadDepth
		logMapNew[field] = updatedAssetTyre.StartThreadDepth
	}

	*updateAssetTyre = *updatedAssetTyre
	return isAnyUpdate

}

func (uc *AssetTyreUseCase) getRedirectLinkAssetTyre(assetTyre *models.AssetTyre) string {
	apiURL := internalConstants.API_STAGING_URL
	if os.Getenv(internalConstants.ENV_APP_ENV) == "production" {
		apiURL = internalConstants.API_URL
	}
	return fmt.Sprintf("http://%s/v1/redirects?type=%s&ref=%s&client_id=%s", apiURL, notifConstants.REDIRECT_TYPE_ASSET_TYRE, assetTyre.AssetID, assetTyre.ClientID)
}

func (uc *AssetTyreUseCase) notifyAfterUpdateAssetTyre(
	ctx context.Context,
	assetID string,
) {
	assetAssignment, err := uc.AssetAssignmentRepo.GetAssetAssignment(
		ctx, uc.DB.DB(), models.AssetAssignmentCondition{
			Where: models.AssetAssignmentWhere{
				AssetID:  assetID,
				Assigned: true,
			},
		})
	if err != nil {
		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Warnf("failed to get asset assignment %v", err)
		}
		return
	}

	assetTyre, err := uc.AssetTyreRepository.GetAssetTyre(ctx, uc.DB.DB(), models.AssetTyreCondition{
		Where: models.AssetTyreWhere{
			AssetID: assetID,
		},
		Preload: models.AssetTyrePreload{
			Tyre:       true,
			Asset:      true,
			AssetBrand: true,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get tyre, err: %v", err)
		return
	}
	href := uc.getRedirectLinkAssetTyre(assetTyre)
	title, err := tmplhelpers.ParseStringTemplate(
		"Tyre {{.serialNumber}} was updated",
		map[string]interface{}{
			"serialNumber": assetTyre.Asset.SerialNumber,
		})
	if err != nil {
		commonlogger.Warnf("failed to create title email %v", err)
		return
	}

	body, err := tmplhelpers.ParseStringTemplate(
		`Asset tyre data was updated<br><br>
		<table>
		{{ range $key, $value := .Data }}
			<tr>
			<td>{{ $key }}</td>: <td>{{ $value }}</td>
			</tr>
		{{ end }}
		<br>
		<a href = "{{.RedirectLink}}"><button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">View Tyre</button></a><br>
		</table>
		<br>`,
		struct {
			Data         map[string]interface{}
			RedirectLink template.URL
		}{
			RedirectLink: template.URL(href),
			Data: map[string]interface{}{
				"Status":                 assetTyre.Asset.StatusLabel(),
				"Serial Number":          assetTyre.Asset.SerialNumber,
				"Brand":                  assetTyre.Asset.Brand.BrandName,
				"Pattern/Type":           assetTyre.Tyre.PatternType,
				"Datetime Of Last Check": assetTyre.DatetimeOfLastCheck,
				"Average RTD":            assetTyre.AverageRTD,
				"Total KM":               assetTyre.TotalKM,
				"Retread Number":         assetTyre.RetreadNumber,
				"DOT Code":               assetTyre.DOTCode,
				"Date Code":              assetTyre.DateCode,
				"Start Thread Depth":     assetTyre.StartThreadDepth,
			},
		},
	)
	if err != nil {
		commonlogger.Warnf("failed to body email %v", err)
		return
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            assetAssignment.UserID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_ASSET_TYRE,
		SourceReferenceID: assetAssignment.AssetID,
		TargetReferenceID: "",
		TargetURL:         "",
		MessageHeader:     title,
		MessageBody:       body,
		ClientID:          assetAssignment.ClientID,
		TypeCode:          "",
		ContentTypeCode:   "",
		ReferenceCode:     notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:    assetID,
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: true,
	})

}

func (uc *AssetTyreUseCase) GetTyreList(ctx context.Context, req dtos.TyreListReq) (commonmodel.ListResponse, error) {
	resp := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return resp, err
	}

	totalRecords, tyres, err := uc.AssetTyreRepository.GetTyreList(ctx, uc.DB.DB(),
		models.GetTyreListParam{
			ListRequest: req.ListRequest,
			Cond: models.TyreCondition{
				Where: models.TyreWhere{
					ClientIDAndGeneral: claim.GetLoggedInClientID(),
					BrandID:            req.BrandID,
				},
				Preload: models.TyrePreload{
					Brand: true,
				},
			},
		},
	)
	if err != nil {
		return resp, err
	}

	if len(tyres) == 0 {
		return resp, nil
	}

	respData := make([]dtos.TyreListResp, 0, len(tyres))
	for _, tyre := range tyres {
		respData = append(respData, dtos.TyreListResp{
			ID:                 tyre.ID,
			PatternType:        tyre.PatternType,
			OriginalTd:         tyre.OriginalTd,
			ConstructionType:   tyre.ConstructionType,
			PlyRating:          tyre.PlyRating,
			LoadRating:         tyre.LoadRating,
			SpeedIndex:         tyre.SpeedIndex,
			StarRating:         tyre.StarRating,
			TRACode:            tyre.TRACode,
			BrandID:            tyre.BrandID,
			IsGeneral:          tyre.IsGeneral(),
			SectionWidth:       tyre.SectionWidth,
			Construction:       tyre.Construction,
			RimDiameter:        tyre.RimDiameter,
			TyreNumber:         tyre.TyreNumber,
			RecommendedRimSize: tyre.RecommendedRimSize,
			Brand: dtos.Brand{
				ID:        tyre.Brand.ID,
				BrandName: tyre.Brand.BrandName,
			},
		})
	}

	resp = commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}

	return resp, nil
}

func (uc *AssetTyreUseCase) GetTyreSizeList(ctx context.Context, req commonmodel.ListRequest) (commonmodel.ListResponse, error) {
	resp := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return resp, err
	}

	totalRecords, tyres, err := uc.AssetTyreRepository.GetTyreSizeList(ctx, uc.DB.DB(),
		models.GetTyreSizeListParam{
			ListRequest: req,
			Cond: models.TyreSizeCondition{
				Where: models.TyreSizeWhere{
					ClientIDAndGeneral: claim.GetLoggedInClientID(),
				},
				Columns: []string{},
			},
		},
	)
	if err != nil {
		return resp, err
	}

	if len(tyres) == 0 {
		return resp, nil
	}

	resp = commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         tyres,
	}

	return resp, nil
}

func (uc *AssetTyreUseCase) GetTyrePatternTypes(ctx context.Context, req commonmodel.ListRequest) (commonmodel.ListResponse, error) {
	resp := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return resp, err
	}

	totalRecords, tyrePatternTypes, err := uc.AssetTyreRepository.GetTyrePatternTypes(ctx, uc.DB.DB(),
		models.GetTyrePatternTypeListParam{
			ListRequest: req,
			Cond: models.TyrePatternTypeCondition{
				Where: models.TyrePatternTypeWhere{
					ClientIDAndGeneral: claim.GetLoggedInClientID(),
				},
				Columns: []string{},
			},
		},
	)
	if err != nil {
		return resp, err
	}

	if len(tyrePatternTypes) == 0 {
		return resp, nil
	}

	resp = commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         tyrePatternTypes,
	}

	return resp, nil
}

func (uc *AssetTyreUseCase) CreateTyre(ctx context.Context, req dtos.CreateTyreReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tyre := &models.Tyre{
		BrandID:            req.BrandID,
		PatternType:        req.PatternType,
		OriginalTd:         req.OriginalTd,
		ConstructionType:   req.ConstructionType,
		PlyRating:          req.PlyRating,
		LoadRating:         req.LoadRating,
		SpeedIndex:         req.SpeedIndex,
		StarRating:         req.StarRating,
		TRACode:            req.TRACode,
		SectionWidth:       req.SectionWidth,
		Construction:       req.Construction,
		RimDiameter:        req.RimDiameter,
		ClientID:           claim.GetLoggedInClientID(),
		RecommendedRimSize: req.RecommendedRimSize,
	}
	err = uc.AssetTyreRepository.CreateTyre(ctx, uc.DB.DB(), tyre)
	if err != nil {
		return nil, err
	}

	resp := dtos.TyreResp{
		ID:                 tyre.ID,
		BrandID:            tyre.BrandID,
		PatternType:        tyre.PatternType,
		OriginalTd:         tyre.OriginalTd,
		ConstructionType:   tyre.ConstructionType,
		PlyRating:          tyre.PlyRating,
		LoadRating:         tyre.LoadRating,
		SpeedIndex:         tyre.SpeedIndex,
		StarRating:         tyre.StarRating,
		TRACode:            tyre.TRACode,
		SectionWidth:       tyre.SectionWidth,
		Construction:       tyre.Construction,
		RimDiameter:        tyre.RimDiameter,
		TyreNumber:         tyre.TyreNumber,
		RecommendedRimSize: tyre.RecommendedRimSize,
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: tyre.ID,
		Data:        resp,
	}, nil
}

func (uc *AssetTyreUseCase) UpdateTyre(ctx context.Context, id string, req dtos.UpdateTyreReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tyre, err := uc.AssetTyreRepository.GetTyre(ctx, uc.DB.DB(), models.TyreCondition{
		Where: models.TyreWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	isAnyUpdate, updateTyre := checkUpdateTyre(tyre, req)

	if !isAnyUpdate {
		return &commonmodel.UpdateResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: tyre.ID,
			Data:        nil,
		}, nil
	}

	err = uc.AssetTyreRepository.UpdateTyre(ctx, uc.DB.WithCtx(ctx).DB(), updateTyre)
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: tyre.ID,
		Data:        nil,
	}, nil
}

func checkUpdateTyre(before *models.Tyre, req dtos.UpdateTyreReq) (bool, *models.Tyre) {
	updateTyre := &models.Tyre{}
	updateTyre.ID = before.ID
	isAnyUpdate := false

	if req.BrandID != "" && req.BrandID != before.BrandID {
		isAnyUpdate = true
		updateTyre.BrandID = req.BrandID
	}

	if req.RecommendedRimSize != "" && req.RecommendedRimSize != before.RecommendedRimSize {
		isAnyUpdate = true
		updateTyre.RecommendedRimSize = req.RecommendedRimSize
	}

	if req.PatternType != "" && req.PatternType != before.PatternType {
		isAnyUpdate = true
		updateTyre.PatternType = req.PatternType
	}

	if req.SectionWidth != "" && req.SectionWidth != before.SectionWidth {
		isAnyUpdate = true
		updateTyre.SectionWidth = req.SectionWidth
	}

	if req.Construction != "" && req.Construction != before.Construction {
		isAnyUpdate = true
		updateTyre.Construction = req.Construction
	}

	if req.RimDiameter != "" && req.RimDiameter != before.RimDiameter {
		isAnyUpdate = true
		updateTyre.RimDiameter = req.RimDiameter
	}

	if req.OriginalTd != 0 && req.OriginalTd != before.OriginalTd {
		isAnyUpdate = true
		updateTyre.OriginalTd = req.OriginalTd
	}

	if req.ConstructionType != "" && req.ConstructionType != before.ConstructionType {
		isAnyUpdate = true
		updateTyre.ConstructionType = req.ConstructionType
	}

	if req.PlyRating != 0 && req.PlyRating != before.PlyRating {
		isAnyUpdate = true
		updateTyre.PlyRating = req.PlyRating
	}

	if req.LoadRating != "" && req.LoadRating != before.LoadRating {
		isAnyUpdate = true
		updateTyre.LoadRating = req.LoadRating
	}

	if req.SpeedIndex != "" && req.SpeedIndex != before.SpeedIndex {
		isAnyUpdate = true
		updateTyre.SpeedIndex = req.SpeedIndex
	}

	if req.StarRating != 0 && req.StarRating != before.StarRating {
		isAnyUpdate = true
		updateTyre.StarRating = req.StarRating
	}

	if req.TRACode != "" && req.TRACode != before.TRACode {
		isAnyUpdate = true
		updateTyre.TRACode = req.TRACode
	}

	return isAnyUpdate, updateTyre
}

func (uc *AssetTyreUseCase) DeleteTyre(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.AssetTyreRepository.GetTyre(ctx, uc.DB.DB(), models.TyreCondition{
		Where: models.TyreWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.AssetTyreRepository.DeleteTyreByID(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *AssetTyreUseCase) GetTyre(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tyre, err := uc.AssetTyreRepository.GetTyre(ctx, uc.DB.DB(), models.TyreCondition{
		Where: models.TyreWhere{
			ID:                 id,
			ClientIDAndGeneral: claim.GetLoggedInClientID(),
		},
		Preload: models.TyrePreload{
			Brand: true,
		},
	})
	if err != nil {
		return nil, err
	}

	_, err = uc.AssetTyreRepository.GetAssetTyre(ctx, uc.DB.DB(), models.AssetTyreCondition{
		Where: models.AssetTyreWhere{
			TyreID: id,
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	isTyreAlreadyUsed := err == nil

	resp := dtos.TyreDetailResp{
		ID:                 id,
		PatternType:        tyre.PatternType,
		OriginalTd:         tyre.OriginalTd,
		ConstructionType:   tyre.ConstructionType,
		PlyRating:          tyre.PlyRating,
		LoadRating:         tyre.LoadRating,
		SpeedIndex:         tyre.SpeedIndex,
		StarRating:         tyre.StarRating,
		TRACode:            tyre.TRACode,
		BrandID:            tyre.BrandID,
		BrandName:          tyre.Brand.BrandName,
		IsGeneral:          tyre.IsGeneral(),
		SectionWidth:       tyre.SectionWidth,
		Construction:       tyre.Construction,
		RimDiameter:        tyre.RimDiameter,
		TyreNumber:         tyre.TyreNumber,
		RecommendedRimSize: tyre.RecommendedRimSize,
		IsEditable:         !isTyreAlreadyUsed && !tyre.IsGeneral(),
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        resp,
	}, nil
}

func logRetread(
	assetTyre *models.AssetTyre, req dtos.RetreadAssetTyreReq,
	logMapNew map[string]interface{},
) error {
	threadSequence := "Thread Sequence"
	logMapNew[threadSequence] = assetTyre.RetreadNumber + 1

	startThreadDepth := "Starth Thread Depth"
	logMapNew[startThreadDepth] = req.StartThreadDepth

	vendorName := "Vendor Name"
	logMapNew[vendorName] = req.VendorName

	brandName := "Brand Name"
	logMapNew[brandName] = req.BrandName

	retreadType := "Retread Type"
	logMapNew[retreadType] = req.RetreadType

	cost := "Cost"
	logMapNew[cost] = req.Cost
	return nil
}

func (uc *AssetTyreUseCase) RetreadAssetTyre(ctx context.Context, assetID string, req dtos.RetreadAssetTyreReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	assetTyre, err := uc.AssetTyreRepository.GetAssetTyre(ctx, uc.DB.DB(), models.AssetTyreCondition{
		Where: models.AssetTyreWhere{
			AssetID:  assetID,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.AssetTyrePreload{
			Asset: true,
		},
	})
	if err != nil {
		return nil, err
	}

	if assetTyre.Asset.AssetStatusCode == constants.ASSET_STATUS_CODE_INSTALLED {
		return nil, errorhandler.ErrNotAllowed("tyre is in use")
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	assetTyre, err = uc.AssetTyreRepository.GetAssetTyre(ctx, tx.DB(), models.AssetTyreCondition{
		Where: models.AssetTyreWhere{
			AssetID:  assetID,
			ClientID: claim.GetLoggedInClientID(),
		},
		IsForUpdate: true,
	})
	if err != nil {
		return nil, err
	}

	if req.TypeCode == "" {
		req.TypeCode = constants.ASSET_TYRE_TREAD_TYPES_RETREAD
	}

	tread := models.AssetTyreTread{
		AssetID:            assetID,
		ThreadSequence:     assetTyre.RetreadNumber + 1,
		AverageRTD:         req.StartThreadDepth,
		TotalKM:            0,
		StartThreadDepth:   req.StartThreadDepth,
		OriginalTd:         req.StartThreadDepth,
		TotalLifetime:      0,
		VendorName:         null.StringFromPtr(&req.VendorName),
		PartnerID:          req.PartnerID,
		BrandName:          req.BrandName,
		RetreadType:        req.RetreadType,
		RetreadDate:        parseDate(req.RetreadDate),
		Cost:               req.Cost,
		TypeCode:           req.TypeCode,
		Notes:              req.Notes,
		TyresTreadConfigID: req.TyresTreadConfigID,
	}

	err = uc.AssetTyreRepository.CreateAssetTyreTread(ctx, tx.DB(), &tread)
	if err != nil {
		return nil, err
	}

	err = uc.AssetTyreRepository.RetreadAssetTyre(ctx, tx.DB(), assetID, req.StartThreadDepth)
	if err != nil {
		return nil, err
	}

	_, err = uc.attachmentUsecase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_TYRE_RETREAD,
		SourceReferenceID: tread.ID,
		TargetReferenceID: assetID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = uc.financeUseCase.CreateJournal(ctx, financeDtos.CreateJournalReq{
		AccountTransactionType: financeConstants.ACCOUNT_TRANSACTION_TYPE_RETREAD_TYRE_COST,
		Date:                   tread.CreatedAt,
		References: []financeDtos.JournalReference{
			{
				ReferenceID: tread.ID,
				SourceCode:  financeConstants.JOURNAL_SOURCE_RETREAD_TYRE_CODE,
			},
			{
				ReferenceID: assetTyre.AssetID,
				SourceCode:  financeConstants.JOURNAL_SOURCE_ASSET_CODE,
			},
		},
		Notes:    "",
		Amount:   int64(req.Cost),
		ClientID: claim.GetLoggedInClientID(),
		UserID:   claim.UserID,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Sucess",
		ReferenceID: tread.ID,
		Data:        nil,
	}, nil
}

func generateAssetTyreBulkUploadFilePath(clientID, name string) string {
	return clientID + "/ASSET_TYRE/BULK_UPLOAD/" + helpers.GenerateSecureFileName(name)
}

func (uc *AssetTyreUseCase) ParseAssetTyreBulkUpload(ctx context.Context, req commonmodel.BulkUploadReq) ([]dtos.BulkUploadAssetTyreReq, []byte, error) {
	file, err := req.FileHeader.Open()
	if err != nil {
		return nil, nil, err
	}
	defer file.Close()

	bufReader := bufio.NewReader(file)
	bytes, err := io.ReadAll(bufReader)
	if err != nil {
		return nil, nil, err
	}

	bytesCopy := make([]byte, len(bytes))
	copy(bytesCopy, bytes)
	data := []dtos.BulkUploadAssetTyreReq{}
	if err := gocsv.UnmarshalBytes(bytesCopy, &data); err != nil {
		return nil, nil, err
	}

	if len(data) > 50 {
		return nil, nil, errorhandler.ErrBadRequest(errorhandler.ErrExceedMaxRow)
	}

	return data, bytes, nil
}

func (uc *AssetTyreUseCase) ValidateAssetTyreBulkUpload(ctx context.Context, req []dtos.BulkUploadAssetTyreReq) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}
	clientID := claim.GetLoggedInClientID()

	brandNumbers := []string{}
	serialNumbers := []string{}
	tyreNumbers := []string{}
	for _, r := range req {
		brandNumbers = append(brandNumbers, r.BrandNo)
		serialNumbers = append(serialNumbers, r.SerialNumber)
		tyreNumbers = append(tyreNumbers, r.TyreNo)
	}

	brands, err := uc.brandRepo.GetBrands(ctx, uc.DB.DB(), models.BrandCondition{
		Where: models.BrandWhere{
			ClientID:     clientID,
			BrandNumbers: brandNumbers,
			Tags:         []string{constants.BRAND_TAG_TYRE},
		},
	})
	if err != nil {
		return err
	}
	mapBrands := map[string]string{}
	for _, brand := range brands {
		mapBrands[brand.BrandNumber] = brand.ID
	}

	assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), models.AssetCondition{
		Where: models.AssetWhere{
			SerialNumbers: serialNumbers,
		},
	})
	if err != nil {
		return err
	}

	mapAssetTyres := map[string]bool{}
	for _, asset := range assets {
		mapAssetTyres[asset.SerialNumber] = true
	}

	tyres, err := uc.AssetTyreRepository.GetTyresV2(ctx, uc.DB.DB(), models.TyreCondition{
		Where: models.TyreWhere{
			Numbers: tyreNumbers,
		},
	})
	if err != nil {
		return err
	}

	mapTyres := map[string]string{}
	for _, tyre := range tyres {
		mapTyres[tyre.TyreNumber] = tyre.ID
	}

	for i := range req {
		req[i].IsValidToProcess = true
		req[i].ValidateBrandNo(mapBrands)
		req[i].ValidateTyreNo(mapTyres)
		req[i].ValidateSerialNumber(mapAssetTyres)
	}

	return nil
}

func (uc *AssetTyreUseCase) AssetTyreBulkUpload(ctx context.Context, fileName string, oriFileByte []byte, req []dtos.BulkUploadAssetTyreReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	numErrorRows := 0
	for i := 0; i < len(req); i++ {
		if req[i].IsSuccess {
			continue
		}

		if !req[i].IsValidToProcess {
			numErrorRows++
			continue
		}

		asset, err := uc.CreateAssetTyre(ctx, &dtos.AssetTyreItemReceiver{
			Name:             req[i].Name,
			BrandID:          req[i].BrandID,
			SerialNumber:     req[i].SerialNumber,
			StatusCode:       constants.ASSET_STATUS_CODE_NEW_STOCK,
			Cost:             req[i].Cost,
			DateCode:         req[i].DateCode,
			DOTCode:          req[i].DOTCode,
			TotalKM:          req[i].TotalKM,
			TotalHm:          req[i].TotalHm,
			TyreID:           req[i].TyreID,
			StartThreadDepth: req[i].StartThreadDepth,
		})
		if err != nil {
			commonlogger.Warnf("failed to process bulk upload asset tyre", i, err)
			req[i].FailedReason = err.Error()
			numErrorRows++
			continue
		}

		req[i].ReferenceID = asset.ID
		req[i].IsSuccess = true
	}

	uploadStatus := uploadConstants.USER_UPLOAD_STATUS_SUCCESS_CODE
	if numErrorRows > 0 {
		if numErrorRows == len(req) {
			uploadStatus = uploadConstants.USER_UPLOAD_STATUS_FAILED_CODE
		} else {
			uploadStatus = uploadConstants.USER_UPLOAD_STATUS_PARTIAL_SUCCESS_CODE
		}
	}

	resultBytes, err := gocsv.MarshalBytes(&req)
	if err != nil {
		return nil, err
	}

	oriFilePath := generateAssetTyreBulkUploadFilePath(claim.GetLoggedInClientID(), fileName)
	err = uc.storageUseCase.UploadCsvFile(ctx, oriFilePath, oriFileByte)
	if err != nil {
		return nil, err
	}

	resultFilePath := generateAssetTyreBulkUploadFilePath(claim.GetLoggedInClientID(), "result_"+fileName)
	err = uc.storageUseCase.UploadCsvFile(ctx, resultFilePath, resultBytes)
	if err != nil {
		return nil, err
	}

	userBulkUpload := &uploadModel.UserBulkUpload{
		ModelV2:            commonmodel.ModelV2{},
		BulkUploadCode:     uploadConstants.BULK_UPLOAD_ASSET_TYRE_CODE,
		StatusCode:         uploadStatus,
		OriginalFilePath:   oriFilePath,
		ResultFilePath:     resultFilePath,
		NumberOfSuccessRow: len(req) - numErrorRows,
		NumberOfFailedRow:  numErrorRows,
	}

	err = uc.uploadRepo.CreateUserBulkUploadData(ctx, uc.DB.WithCtx(ctx).DB(), userBulkUpload)
	if err != nil {
		return nil, err
	}

	resp := uploadDto.UserBulkUploadResp{
		ID:                 userBulkUpload.ID,
		BulkUploadCode:     userBulkUpload.BulkUploadCode,
		StatusCode:         userBulkUpload.StatusCode,
		OriginalFilePath:   userBulkUpload.OriginalFilePath,
		ResultFilePath:     userBulkUpload.ResultFilePath,
		NumberOfSuccessRow: userBulkUpload.NumberOfSuccessRow,
		NumberOfFailedRow:  userBulkUpload.NumberOfFailedRow,
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: userBulkUpload.ID,
		Data:        resp,
	}, nil
}

func (uc *AssetTyreUseCase) GetTyresCSV(ctx context.Context) ([]dtos.TyreCSV, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	tyreCSV := []dtos.TyreCSV{}

	tyres, err := uc.AssetTyreRepository.GetTyresCSV(ctx, uc.DB.DB(), models.TyreCondition{
		Where: models.TyreWhere{
			ClientID: claim.GetLoggedInClientID(),
		},
	})

	if err != nil {
		return nil, err
	}
	for _, v := range tyres {
		columnData := dtos.TyreCSV{
			TyreNo:             v.TyreNumber,
			Brand:              v.Brand.BrandName,
			PatternType:        v.PatternType,
			AspectRatioConsRim: fmt.Sprintf("%s %s %s", v.SectionWidth, v.Construction, v.RimDiameter),
			OTD:                v.OriginalTd,
			TubeType:           v.ConstructionType,
			PlyRating:          v.PlyRating,
			LoadIndex:          v.LoadRating,
			Speedsymbol:        v.SpeedIndex,
			TRACode:            v.TRACode,
			StarRating:         v.StarRating,
		}
		tyreCSV = append(tyreCSV, columnData)
	}
	return tyreCSV, nil
}

func (uc *AssetTyreUseCase) PopulatePeriodicAssetTyreStatsHistories(ctx context.Context) (*commonmodel.CreateResponse, error) {
	err := uc.AssetTyreRepository.PopulatePeriodicAssetTyreStatsHistory(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *AssetTyreUseCase) GetLastMonthAssetTyreStatsHistory(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	now := time.Now()

	// Get the first day of the current month
	firstDayOfCurrentMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// Subtract one day to get the last day of the previous month
	lastDayOfLastMonth := firstDayOfCurrentMonth.AddDate(0, 0, -1)

	assetTyreStatsHistory, err := uc.AssetTyreRepository.GetAssetTyreStatsHistory(ctx, uc.DB.DB(), lastDayOfLastMonth, assetID, claim.GetLoggedInClientID())
	if err != nil {
		if errorhandler.IsErrNotFound(err) {
			return &commonmodel.DetailResponse{
				Success:     true,
				Message:     "Success",
				ReferenceID: "",
				Data:        nil,
			}, nil
		}
		return nil, err
	}

	resp := dtos.AssetTyreStatsHistoryResp{
		Datetime:      lastDayOfLastMonth,
		AssetID:       assetID,
		TotalKM:       assetTyreStatsHistory.TotalKM,
		TotalHM:       calculationhelpers.Div100(assetTyreStatsHistory.TotalHM),
		TotalLifetime: assetTyreStatsHistory.TotalLifetime,
	}

	journalRefSumAmountHistory, err := uc.financeUseCase.FinanceRepository.GetJournalRefSumAmountHistory(ctx, uc.DB.DB(), lastDayOfLastMonth, assetID, "ASSET", claim.GetLoggedInClientID())
	if err != nil {
		if errorhandler.IsErrNotFound(err) {
			return &commonmodel.DetailResponse{
				Success:     true,
				Message:     "Success",
				ReferenceID: "",
				Data:        resp,
			}, nil
		}
		return nil, err
	}

	resp.TotalAssetCost = &journalRefSumAmountHistory.Amount
	if resp.TotalKM > 0 {
		totalCPK := float64(*resp.TotalAssetCost) / float64(resp.TotalKM)
		resp.TotalCPK = &totalCPK
	}

	if resp.TotalHM > 0 {
		totalCPH := float64(*resp.TotalAssetCost) / resp.TotalHM
		resp.TotalCPH = &totalCPH
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        resp,
	}, nil
}

func (uc *AssetTyreUseCase) ExportAssetTyreByID(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	assetTyre, err := uc.AssetTyreRepository.GetAssetTyre(ctx, uc.DB.DB(), models.AssetTyreCondition{
		Where: models.AssetTyreWhere{
			ClientID: claim.GetLoggedInClientID(),
			AssetID:  id,
		},
		Preload: models.AssetTyrePreload{
			Tyre:               true,
			Asset:              true,
			AssetBrand:         true,
			AssetLinkedVehicle: true,
			RetreadTyre:        true,
			RetreadTyreType:    true,
			AssetStatus:        true,
		},
	})
	if err != nil {
		return nil, err
	}

	_, err = uc.AssetLinkedRepo.GetAssetTyrePositionById(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	partnerIDs := []string{}
	for i := range assetTyre.RetreadTyre {
		if assetTyre.RetreadTyre[i].PartnerID != "" {
			partnerIDs = append(partnerIDs, assetTyre.RetreadTyre[i].PartnerID)
		}
	}

	mapPartners := map[string]userIdentityModels.Partner{}
	if len(partnerIDs) > 0 {
		partners, err := uc.partnerRepo.GetPartners(ctx, uc.DB.DB(), userIdentityModels.PartnerCondition{
			Where: userIdentityModels.PartnerWhere{
				IDs: partnerIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for i := range partners {
			mapPartners[partners[i].ID] = partners[i]
		}
	}

	journalRefIDs := make([]string, 0, len(assetTyre.RetreadTyre)+1)
	journalRefIDs = append(journalRefIDs, id)
	lifetimeKm := 0
	var lifetimeHm int
	lifetimeActive := 0
	for _, tread := range assetTyre.RetreadTyre {
		journalRefIDs = append(journalRefIDs, tread.ID)
		lifetimeKm += tread.TotalKM
		lifetimeHm += tread.TotalHm
		lifetimeActive += tread.TotalLifetime
	}

	refAmounts, err := uc.financeUseCase.GetTotalCostsByReferenceIDs(ctx, journalRefIDs)
	if err != nil {
		return nil, err
	}

	mapRefAmounts := map[string]financeDtos.ReferenceTotalAmount{}
	for _, refAmount := range refAmounts {
		mapRefAmounts[refAmount.ReferenceID] = refAmount
	}

	var lifetimeCPK float64 = 0
	if lifetimeKm > 0 {
		lifetimeCPK = float64(mapRefAmounts[id].Amount) / float64(lifetimeKm)
	}

	var lifetimeCPH float64 = 0
	if lifetimeHm > 0 {
		lifetimeCPH = float64(mapRefAmounts[id].Amount) / calculationhelpers.Div100(lifetimeHm)
	}

	resp := dtos.AssetTyreDetail{
		Cost:      mapRefAmounts[assetTyre.AssetID].Amount,
		CreatedAt: assetTyre.CreatedAt,
		UpdatedAt: assetTyre.UpdatedAt,
		AssetID:   assetTyre.AssetID,

		Asset:                     *dtos.BuildAssetResp(&assetTyre.Asset),
		DatetimeOfLastCheck:       assetTyre.DatetimeOfLastCheck,
		AverageRTD:                assetTyre.AverageRTD,
		UtilizationRatePercentage: assetTyre.UtilizationRatePercentage,
		TotalKm:                   assetTyre.TotalKM,
		TotalHm:                   calculationhelpers.Div100(assetTyre.TotalHm),
		ProjectedLifeKM:           assetTyre.ProjectedLifeKM(),
		ProjectedLifeHm:           assetTyre.ProjectedLifeHm(),
		RetreadNumber:             assetTyre.RetreadNumber,
		DotCode:                   assetTyre.DOTCode.String,
		DateCode:                  assetTyre.DateCode.String,
		ClientID:                  assetTyre.ClientID,
		TyreID:                    assetTyre.TyreID,
		StartThreadDepth:          assetTyre.StartThreadDepth,
		OriginalTd:                assetTyre.OriginalTd,
		TotalLifetime:             lifetimeActive,
		LifetimeKM:                lifetimeKm,
		LifetimeCPK:               lifetimeCPK,
		LifetimeCPH:               lifetimeCPH,
		LifetimeHM:                calculationhelpers.Div100(lifetimeHm),
		PartnerOwnerID:            assetTyre.Asset.PartnerOwnerID,
		PartnerOwnerNo:            assetTyre.Asset.PartnerOwnerNo,
		PartnerOwnerName:          assetTyre.Asset.PartnerOwnerName,
		Tyre: dtos.Tyre{
			ID:               assetTyre.Tyre.ID,
			CreatedAt:        assetTyre.Tyre.CreatedAt,
			UpdatedAt:        assetTyre.Tyre.UpdatedAt,
			BrandID:          assetTyre.Tyre.BrandID,
			PatternType:      assetTyre.Tyre.PatternType,
			OriginalTd:       assetTyre.Tyre.OriginalTd,
			ConstructionType: assetTyre.Tyre.ConstructionType,
			PlyRating:        assetTyre.Tyre.PlyRating,
			LoadRating:       assetTyre.Tyre.LoadRating,
			SpeedIndex:       assetTyre.Tyre.SpeedIndex,
			StarRating:       assetTyre.Tyre.StarRating,
			TraCode:          assetTyre.Tyre.TRACode,
			ClientID:         assetTyre.Tyre.ClientID,
			SectionWidth:     assetTyre.Tyre.SectionWidth,
			Construction:     assetTyre.Tyre.Construction,
			RimDiameter:      assetTyre.Tyre.RimDiameter,
		},
	}

	if assetTyre.AssetLinked != nil {
		tyrePosition := 0
		if assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre != nil {
			tyrePosition = assetTyre.AssetLinked.AssetLinkedAssetVehicleTyre.TyrePosition
		}

		resp.AssetVehicleLinked = &dtos.AssetVehicleLinked{
			ID:                 assetTyre.AssetLinked.ParentAssetID,
			RegistrationNumber: assetTyre.AssetLinked.ParentAsset.RegistrationNumber,
			SerialNumber:       assetTyre.AssetLinked.ParentAsset.Asset.SerialNumber,
			ReferenceNumber:    assetTyre.AssetLinked.ParentAsset.Asset.ReferenceNumber,
			VehicleBodyType: dtos.VehicleBodyType{
				Code:        assetTyre.AssetLinked.ParentAsset.AssetVehicleBodyType.ID,
				Label:       assetTyre.AssetLinked.ParentAsset.AssetVehicleBodyType.Label,
				Description: assetTyre.AssetLinked.ParentAsset.AssetVehicleBodyType.Description,
			},
			Name:           assetTyre.AssetLinked.ParentAsset.Asset.Name,
			LinkedDatetime: assetTyre.AssetLinked.LinkedDatetime,
			TyrePosition:   tyrePosition,
		}

	}
	retreads := []dtos.RetreadAssetTyreRes{}
	for index, val := range assetTyre.RetreadTyre {
		/*
			TODO: Need to get back to this code and think about other way to do it
		*/
		tread := dtos.BuildRetreadAssetTyreRes(assetTyre, val, mapRefAmounts[val.ID], mapPartners[val.PartnerID].Name, index == len(assetTyre.RetreadTyre)-1)
		retreads = append(retreads, tread)
	}
	resp.RetreadTyre = retreads

	inspectionTyre, err := uc.AssetInspectionTyreRepository.GetAssetInspectionTyre(ctx, uc.DB.DB(), models.AssetInspectionTyreCondition{
		Where: models.AssetInspectionTyreWhere{
			AssetID: id,
		},
		Preload: models.AssetInspectionTyrePreload{
			AssetInspection: true,
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if err == nil {
		assetInspectionInspectedByUserName := ""
		user, err := uc.userIdentityRepo.GetUser(ctx, uc.DB.DB(), userIdentityModels.UserCondition{
			Where: userIdentityModels.UserWhere{
				ID: inspectionTyre.AssetInspection.InspectByUserID,
			},
		})
		if err == nil {
			assetInspectionInspectedByUserName = user.GetName()
		}
		resp.LastInspectionTyre = &dtos.GetAssetInspectionTyresResponse{
			ID:                                 inspectionTyre.ID,
			AssetID:                            inspectionTyre.AssetTyreID,
			Remark:                             inspectionTyre.Remark,
			AssetAssignmentID:                  inspectionTyre.AssetAssignmentID,
			Pressure:                           inspectionTyre.Pressure,
			RDT1:                               inspectionTyre.RDT1,
			RDT2:                               inspectionTyre.RDT2,
			RDT3:                               inspectionTyre.RDT3,
			RDT4:                               inspectionTyre.RDT4,
			TyrePosition:                       inspectionTyre.TyrePosition,
			AverageRTD:                         inspectionTyre.AverageRTD,
			AssetInspectionID:                  inspectionTyre.AssetInspectionID,
			PressureStatusCode:                 inspectionTyre.PressureStatusCode,
			InspectionNumber:                   inspectionTyre.AssetInspection.InspectionNumber,
			AssetInspectionCreatedAt:           inspectionTyre.AssetInspection.CreatedAt,
			AssetInspectionInspectedByUserId:   inspectionTyre.AssetInspection.InspectByUserID,
			AssetInspectionInspectedByUserName: assetInspectionInspectedByUserName,
			TyreKM:                             inspectionTyre.TyreKM,
			TyreHm:                             calculationhelpers.Div100(inspectionTyre.TyreHm),
			FailedVisualChecking:               inspectionTyre.FailedVisualChecking,
			RequireRotationTyre:                inspectionTyre.RequireRotationTyre,
			RequireSpooringVehicle:             inspectionTyre.RequireSpooringVehicle,
			CustomSerialNumber:                 inspectionTyre.CustomSerialNumber,
			DeviceID:                           inspectionTyre.DeviceID,
			SourceTypeCode:                     inspectionTyre.SourceTypeCode,
		}
	}

	// USE HTML TEMPLATING TO APPEND DATA
	type PageData struct {
		AssetTyreDetail  dtos.AssetTyreDetail
		AssetStatusLabel string
	}
	templateData := PageData{
		AssetTyreDetail:  resp,
		AssetStatusLabel: assetTyre.Asset.AssetStatus.Label,
	}
	templateHTML := template.Must(template.ParseFiles("./statics/print-template/tyre_detail.html"))
	var templateBuff bytes.Buffer
	err = templateHTML.Execute(&templateBuff, &templateData)
	if err != nil {
		return nil, err
	}

	// GENERATE PDF
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		commonlogger.Errorf("Error in initializing pdf generator", err.Error())
		return nil, err
	}
	pdfg.Orientation.Set(wkhtmltopdf.OrientationPortrait)
	pdfg.AddPage(wkhtmltopdf.NewPageReader(bytes.NewBuffer(templateBuff.Bytes())))

	err = pdfg.Create()
	if err != nil {
		commonlogger.Errorf("Error in creating pdf", err.Error())
		return nil, err
	}

	pdfgBuffer := pdfg.Buffer()
	signedUrl, pdfHeader, err := uc.storageUseCase.GenerateExportPDFSignedUrl(ctx, pdfgBuffer, "Asset_Tyre_Detail")
	if err != nil {
		commonlogger.Errorf("Error in generating exported pdf download url", err)
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: signedUrl,
		Data:        pdfHeader,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyresTreadConfigList(ctx context.Context, req dtos.GetAssetTyresTreadConfigRequest) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyresTreadConfig, err := uc.AssetTyreRepository.GetAssetTyresTreadConfigList(
		ctx, uc.DB.DB(),
		models.GetAssetTyresTreadConfigListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyresTreadConfigCondition{
				Where: models.AssetTyresTreadConfigWhere{
					ClientID:    claim.GetLoggedInClientID(),
					BrandNames:  req.BrandNames,
					BrandName:   req.BrandName,
					RetreadType: req.RetreadType,
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	if len(assetTyresTreadConfig) == 0 {
		return &commonmodel.ListResponse{
			TotalRecords: totalRecords,
			PageSize:     req.ListRequest.PageSize,
			PageNo:       req.ListRequest.PageNo,
			Data:         nil,
		}, nil
	}

	respData := make([]dtos.AssetTyresTreadConfigListItemResp, 0, len(assetTyresTreadConfig))
	for _, treadConfig := range assetTyresTreadConfig {
		respData = append(respData, dtos.AssetTyresTreadConfigListItemResp{
			ID:                 treadConfig.ID,
			BrandName:          treadConfig.BrandName,
			RetreadType:        treadConfig.RetreadType,
			OriginalTreadDepth: treadConfig.OriginalTreadDepth,
			Width:              treadConfig.Width,
			Weight:             treadConfig.Weight,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.ListRequest.PageSize,
		PageNo:       req.ListRequest.PageNo,
		Data:         respData,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyresTreadConfigByID(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	treadConfig, err := uc.AssetTyreRepository.GetAssetTyresTreadConfig(ctx, uc.DB.DB(), models.AssetTyresTreadConfigCondition{
		Where: models.AssetTyresTreadConfigWhere{
			ClientID: claim.GetLoggedInClientID(),
			ID:       id,
		},
	})
	if err != nil {
		return nil, err
	}

	// CHECK WHETHER CONFIG IS USED BY ASSET TYRE TREADS
	isInUse := false
	_, err = uc.AssetTyreRepository.GetAssetTyreTread(ctx, uc.DB.DB(), models.AssetTyreTreadCondition{
		Where: models.AssetTyreTreadWhere{
			ClientID:      claim.GetLoggedInClientID(),
			TreadConfigID: id,
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}
	if err == nil {
		isInUse = true
	}

	resp := dtos.AssetTyresTreadConfigDetail{
		ID:                 treadConfig.ID,
		BrandName:          treadConfig.BrandName,
		RetreadType:        treadConfig.RetreadType,
		OriginalTreadDepth: treadConfig.OriginalTreadDepth,
		Width:              treadConfig.Width,
		Weight:             treadConfig.Weight,
		IsInUse:            isInUse,
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        resp,
	}, nil
}

func (uc *AssetTyreUseCase) CreateAssetTyresTreadConfig(ctx context.Context, req dtos.CreateOrUpdateAssetTyresTreadConfigRequest) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	treadConfig := &models.AssetTyresTreadConfig{
		BrandName:          req.BrandName,
		RetreadType:        req.RetreadType,
		OriginalTreadDepth: req.OriginalTreadDepth,
		Width:              req.Width,
		Weight:             req.Weight,
		ModelV2: commonmodel.ModelV2{
			ClientID: claim.GetLoggedInClientID(),
		},
	}
	err = uc.AssetTyreRepository.CreateAssetTyresTreadConfig(ctx, tx.DB(), treadConfig)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	resp := dtos.AssetTyresTreadConfigDetail{
		ID:                 treadConfig.ID,
		BrandName:          treadConfig.BrandName,
		RetreadType:        treadConfig.RetreadType,
		OriginalTreadDepth: treadConfig.OriginalTreadDepth,
		Width:              treadConfig.Width,
		Weight:             treadConfig.Weight,
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: treadConfig.ID,
		Data:        resp,
	}, nil
}

func (uc *AssetTyreUseCase) UpdateAssetTyresTreadConfig(ctx context.Context, id string, req dtos.CreateOrUpdateAssetTyresTreadConfigRequest) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	rethreadConfigCount, _, err := uc.AssetTyreRepository.GetAssetTyreTreadList(
		ctx,
		uc.DB.DB(),
		models.GetAssetTyreTreadListParam{
			ListRequest: commonmodel.ListRequest{
				PageSize: 1,
				PageNo:   1,
			},
			Cond: models.AssetTyreTreadCondition{
				Where: models.AssetTyreTreadWhere{
					ClientID:      claim.GetLoggedInClientID(),
					TreadConfigID: id,
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}
	if rethreadConfigCount > 0 {
		return nil, errorhandler.ErrBadRequest("RETREAD_CONFIG_IS_IN_USE")
	}

	updatedTreadConfig := models.AssetTyresTreadConfig{
		BrandName:          req.BrandName,
		RetreadType:        req.RetreadType,
		OriginalTreadDepth: req.OriginalTreadDepth,
		Width:              req.Width,
		Weight:             req.Weight,
	}

	err = uc.AssetTyreRepository.UpdateAssetTyresTreadConfig(ctx, tx.DB(), id, &updatedTreadConfig)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	resp := dtos.AssetTyresTreadConfigDetail{
		ID:                 id,
		BrandName:          updatedTreadConfig.BrandName,
		RetreadType:        updatedTreadConfig.RetreadType,
		OriginalTreadDepth: updatedTreadConfig.OriginalTreadDepth,
		Width:              updatedTreadConfig.Width,
		Weight:             updatedTreadConfig.Weight,
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Sucess",
		ReferenceID: id,
		Data:        resp,
	}, nil
}

func (uc *AssetTyreUseCase) DeleteAssetTyresTreadConfig(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	tyreTread, _ := uc.AssetTyreRepository.GetAssetTyreTread(ctx, uc.DB.DB(), models.AssetTyreTreadCondition{
		Where: models.AssetTyreTreadWhere{
			TreadConfigID: id,
		},
	})
	if tyreTread != nil {
		return nil, errorhandler.ErrBadRequest("TYRE_TREAD_CONFIG_IN_USE")
	}

	_, err = uc.AssetTyreRepository.GetAssetTyresTreadConfig(ctx, uc.DB.DB(), models.AssetTyresTreadConfigCondition{
		Where: models.AssetTyresTreadConfigWhere{
			ClientID: claim.GetLoggedInClientID(),
			ID:       id,
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.AssetTyreRepository.DeleteAssetTyresTreadConfig(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *AssetTyreUseCase) GetAssetTyresTreadConfigBrands(ctx context.Context, req dtos.GetAssetTyresTreadConfigRequest) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	totalRecords, assetTyresTreadConfig, err := uc.AssetTyreRepository.GetAssetTyresTreadConfigBrandList(
		ctx, uc.DB.DB(),
		models.GetAssetTyresTreadConfigListParam{
			ListRequest: req.ListRequest,
			Cond: models.AssetTyresTreadConfigCondition{
				Where: models.AssetTyresTreadConfigWhere{
					ClientID: claim.GetLoggedInClientID(),
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	if len(assetTyresTreadConfig) == 0 {
		return &commonmodel.ListResponse{
			TotalRecords: totalRecords,
			PageSize:     req.ListRequest.PageSize,
			PageNo:       req.ListRequest.PageNo,
			Data:         nil,
		}, nil
	}

	respData := make([]dtos.AssetTyresTreadConfigBrandListItemResp, 0, len(assetTyresTreadConfig))
	for _, treadConfig := range assetTyresTreadConfig {
		respData = append(respData, dtos.AssetTyresTreadConfigBrandListItemResp{
			BrandName: treadConfig.BrandName,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.ListRequest.PageSize,
		PageNo:       req.ListRequest.PageNo,
		Data:         respData,
	}, nil
}
