package handler

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/usecase"
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

type VehicleTargetTyreRemovalHandler struct {
	VehicleTargetTyreRemovalUseCase *usecase.VehicleTargetTyreRemovalUseCase
}

func NewVehicleTargetTyreRemovalHandler(vehicleTargetTyreRemovalUseCase *usecase.VehicleTargetTyreRemovalUseCase) *VehicleTargetTyreRemovalHandler {
	return &VehicleTargetTyreRemovalHandler{
		VehicleTargetTyreRemovalUseCase: vehicleTargetTyreRemovalUseCase,
	}
}

// CreateVehicleTargetTyreRemoval handles POST /v1/vehicle-target-tyre-removals
func (h *VehicleTargetTyreRemovalHandler) CreateVehicleTargetTyreRemoval(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateVehicleTargetTyreRemovalReq

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.VehicleTargetTyreRemovalUseCase.CreateVehicleTargetTyreRemoval(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

// UpdateVehicleTargetTyreRemoval handles PUT /v1/vehicle-target-tyre-removals/:id
func (h *VehicleTargetTyreRemovalHandler) UpdateVehicleTargetTyreRemoval(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.UpdateVehicleTargetTyreRemovalReq

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.VehicleTargetTyreRemovalUseCase.UpdateVehicleTargetTyreRemoval(ctx, id, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetVehicleTargetTyreRemovalList handles GET /v1/vehicle-target-tyre-removals
func (h *VehicleTargetTyreRemovalHandler) GetVehicleTargetTyreRemovalList(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.VehicleTargetTyreRemovalListReq

	if err := c.BindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.VehicleTargetTyreRemovalUseCase.GetVehicleTargetTyreRemovalList(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetVehicleTargetTyreRemovalByID handles GET /v1/vehicle-target-tyre-removals/:id
func (h *VehicleTargetTyreRemovalHandler) GetVehicleTargetTyreRemovalByID(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "id parameter is required"})
		return
	}

	resp, err := h.VehicleTargetTyreRemovalUseCase.GetVehicleTargetTyreRemovalByID(ctx, id)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *VehicleTargetTyreRemovalHandler) CheckTargetRTDAvailability(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CheckTargetRTDAvailabilityReq

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.VehicleTargetTyreRemovalUseCase.CheckTargetRTDAvailability(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}
