package handler

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/usecase"
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

type LocationHandler struct {
	locationUseCase *usecase.LocationUseCase
}

func NewLocationHandler(
	locationUseCase *usecase.LocationUseCase,
) *LocationHandler {
	return &LocationHandler{
		locationUseCase: locationUseCase,
	}
}

func (h *LocationHandler) GetLocations(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.LocationListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.locationUseCase.GetLocations(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.<PERSON>(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.<PERSON>(http.StatusOK, resp)
}

func (h *LocationHandler) GetLocation(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	resp, err := h.locationUseCase.GetLocation(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)

}

func (h *LocationHandler) CreateLocation(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateLocation
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.locationUseCase.CreateLocation(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *LocationHandler) UpdateLocation(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.UpdateLocation
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.locationUseCase.UpdateLocation(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *LocationHandler) DeleteLocation(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	resp, err := h.locationUseCase.DeleteteLocation(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
