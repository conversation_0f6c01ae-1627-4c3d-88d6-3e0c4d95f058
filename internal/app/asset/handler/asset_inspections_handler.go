package handler

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type AssetInspectionHandler struct {
	AssetInspectionUseCase usecase.AssetInspectionUseCase
}

func NewAssetInspectionHandler(useCase usecase.AssetInspectionUseCase) *AssetInspectionHandler {
	return &AssetInspectionHandler{
		AssetInspectionUseCase: useCase,
	}
}
func (h *AssetInspectionHandler) GetAssetInspections(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetInspectionListReq{}
	err := c.Bind<PERSON>uery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetInspectionUseCase.GetAssetInspectionList(ctx, req, false)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) GetAssetInspectionsDigiSpect(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetInspectionUseCase.GetAssetInspectionList(ctx, req, true)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) GetAssetInspectionsTickets(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetInspectionUseCase.GetAssetInspectionTicketList(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) GetAssetInspectionVehicles(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	vehicles, err := h.AssetInspectionUseCase.GetAssetInspectionVehicles(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, vehicles)
}

func (h *AssetInspectionHandler) GetLatestAssetInspectionVehicle(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")
	resp, err := h.AssetInspectionUseCase.GetLatestAssetInspectionVehicle(ctx, assetID)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) GetAssetInspectionTyres(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	tyresResponse, err := h.AssetInspectionUseCase.GetAssetInspectionTyres(ctx, req, false)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, tyresResponse)
}

func (h *AssetInspectionHandler) GetAssetInspectionTyresDigiSpect(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	tyresResponse, err := h.AssetInspectionUseCase.GetAssetInspectionTyres(ctx, req, true)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, tyresResponse)
}

func (h *AssetInspectionHandler) ExportAssetInspectionTyres(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	generateExcelResponse, err := h.AssetInspectionUseCase.ExportAssetInspectionTyres(ctx, req)
	if err != nil {
		commonlogger.Errorf("Failed in exporting asset inspection tyres: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed in exporting asset inspection tyres"})
		return
	}

	c.JSON(http.StatusOK, generateExcelResponse)
}

func (h *AssetInspectionHandler) ExportAssetInspectionTyresInspectionViewDigispect(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.ExportInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	resp, err := h.AssetInspectionUseCase.ExportAssetInspectionTyresV2(ctx, false, true, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ExportAssetInspectionTyresTyreViewDigispect(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.ExportInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	resp, err := h.AssetInspectionUseCase.ExportAssetInspectionTyresV2(ctx, true, true, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ExportAssetInspectionTyresInspectionView(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.ExportInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	resp, err := h.AssetInspectionUseCase.ExportAssetInspectionTyresInspectionList(ctx, false, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ExportAssetInspectionTyresTyreView(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.ExportInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	resp, err := h.AssetInspectionUseCase.ExportAssetInspectionTyresInspectionList(ctx, true, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ExportAssetInspectionTyresFull(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	generateExcelResponse, err := h.AssetInspectionUseCase.ExportAssetInspectionTyresFull(ctx, req)
	if err != nil {
		commonlogger.Errorf("Failed in exporting asset inspection tyres: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed in exporting asset inspection tyres"})
		return
	}

	c.JSON(http.StatusOK, generateExcelResponse)
}

func (h *AssetInspectionHandler) CreateAssetInspection(c *gin.Context) {
	ctx := c.Request.Context()

	var req dtos.CreateAssetInspectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.CreateAssetInspectionVehicleTyre(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AssetInspectionHandler) CreateAssetInspectionChildTyres(c *gin.Context) {
	ctx := c.Request.Context()

	var req dtos.CreateAssetInspectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	id := c.Param("id")
	resp, err := h.AssetInspectionUseCase.CreateAssetInspectionChildTyres(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AssetInspectionHandler) UpsertAssetInspectionAssignment(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.UpsertAssetInspectionAssignment
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.UpsertAssetInspectionAssignment(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) GetAssetInspectionAssignmentsByInspectionID(c *gin.Context) {
	ctx := c.Request.Context()
	ticketID := c.Param("id")

	resp, err := h.AssetInspectionUseCase.GetAssetInspectionAssignmentsByInspectionID(ctx, ticketID)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) UpdateAssetInspectionStatus(c *gin.Context) {
	ctx := c.Request.Context()

	id := c.Param("id")
	var req dtos.UpdateInspectionStatusReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.UpdateInspectionStatus(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AssetInspectionHandler) ExportAssetInspectionTyresDetailPDF(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	req := dtos.GetInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	generateExcelResponse, err := h.AssetInspectionUseCase.ExportAssetInspectionTyresDetailPDF(ctx, id, req)
	if err != nil {
		commonlogger.Errorf("Failed in exporting asset inspection tyres detail: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed in exporting asset inspection tyres detail"})
		return
	}

	c.JSON(http.StatusOK, generateExcelResponse)
}

func (h *AssetInspectionHandler) ExportAssetInspectionTyresDetailPDFFull(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	req := dtos.GetInspectionListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	req.Normalize()
	generateExcelResponse, err := h.AssetInspectionUseCase.ExportAssetInspectionTyresDetailPDFFull(ctx, id, req)
	if err != nil {
		commonlogger.Errorf("Failed in exporting asset inspection tyres detail: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed in exporting asset inspection tyres detail"})
		return
	}

	c.JSON(http.StatusOK, generateExcelResponse)
}

func (h *AssetInspectionHandler) GetAssetInspectionWorkshopPrint(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req struct {
		Sections []string `form:"sections"`
	}

	err := c.BindQuery(&req)
	if err != nil {
		commonlogger.Errorf("Failed when get workshop print request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	workshopPrintResponse, err := h.AssetInspectionUseCase.PrintWorkshopInspectionHTML(ctx, id, req.Sections)
	if err != nil {
		commonlogger.Errorf("Failed in printing workshop inspection HTML: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed in printing workshop inspection HTML"})
		return
	}

	c.JSON(http.StatusOK, workshopPrintResponse)
}

func (h *AssetInspectionHandler) GetLastInspectionByDigispectVehicle(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	resp, err := h.AssetInspectionUseCase.GetLastInspectionByDigispectVehicle(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartCountSingleAndLinkedInspections(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartCountSingleAndLinkedInspections(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartVehicleInspectionFrequency(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartVehicleInspectionFrequency(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartTyreInspectionPerDate(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartTyreInspectionPerDate(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartVehicleInspectionPerDate(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartVehicleInspectionPerDate(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartTop5TyreBrandBySize(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartTop5TyreBrandBySize(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartTop5VehicleBrands(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartTop5VehicleBrands(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartInspectionLocations(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartInspectionLocations(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartInspectionsByInspectorPerDate(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartInspectionsByInspectorPerDate(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartTotalInspections(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartTotalInspections(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartTotalCustomers(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartTotalCustomers(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartTotalVehiclesInspected(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartTotalVehiclesInspected(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartTotalTyresInspected(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartTotalTyresInspected(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartTotalInspectors(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartTotalInspectors(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartTop5InspectedCustomers(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartTop5InspectedCustomers(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetInspectionHandler) ChartCustomersByInspectionCount(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.InspectionChartReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetInspectionUseCase.ChartCustomersByInspectionCount(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
