package dtos

import (
	"assetfindr/pkg/common/commonmodel"
	"time"
)

type CreateVehicleTargetTyreRemovalReq struct {
	TargetRTD float64  `json:"target_rtd"`
	AssetIDs  []string `json:"asset_ids"`
}

type UpdateVehicleTargetTyreRemovalReq struct {
	TargetRTD       float64  `json:"target_rtd"`
	AssetIDs        []string `json:"asset_ids"`
	DeletedAssetIDs []string `json:"deleted_asset_ids"`
}

type VehicleTargetTyreRemovalListReq struct {
	commonmodel.ListRequest
}

func (r *VehicleTargetTyreRemovalListReq) Normalize() {
	r.ListRequest.Normalize()
}

type VehicleTargetTyreRemovalListResp struct {
	ID          string    `json:"id"`
	TargetRTD   float64   `json:"target_rtd"`
	CountAsset  int       `json:"count_asset"`
	AssetIdents []string  `json:"asset_idents"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type VehicleTargetTyreRemovalDetailResp struct {
	ID        string    `json:"id"`
	TargetRTD float64   `json:"target_rtd"`
	AssetIDs  []string  `json:"asset_ids"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	CreatedBy string    `json:"created_by"`
	UpdatedBy string    `json:"updated_by"`
}

type VehicleTargetTyreRemovalAssetDetail struct {
	ID              string `json:"id"`
	Name            string `json:"name"`
	SerialNumber    string `json:"serial_number"`
	ReferenceNumber string `json:"reference_number"`
	BrandName       string `json:"brand_name"`
	StatusCode      string `json:"status_code"`
}
