package dtos

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/pkg/common/commonmodel"

	"time"

	"gopkg.in/guregu/null.v4"
)

type CreateVehicleTargetTyreRemovalReq struct {
	TargetRTD float64  `json:"target_rtd"`
	AssetIDs  []string `json:"asset_ids"`
}

type UpdateVehicleTargetTyreRemovalReq struct {
	TargetRTD       float64  `json:"target_rtd"`
	AssetIDs        []string `json:"asset_ids"`
	DeletedAssetIDs []string `json:"deleted_asset_ids"`
}

type VehicleTargetTyreRemovalListReq struct {
	commonmodel.ListRequest
}

func (r *VehicleTargetTyreRemovalListReq) Normalize() {
	r.ListRequest.Normalize()
}

type VehicleTargetTyreRemovalListResp struct {
	ID          string    `json:"id"`
	TargetRTD   float64   `json:"target_rtd"`
	CountAsset  int       `json:"count_asset"`
	AssetIdents []string  `json:"asset_idents"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

func BuildVehicleTargetTyreRemovalListResp(removals []models.VehicleTargetTyreRemoval) []VehicleTargetTyreRemovalListResp {
	responseData := []VehicleTargetTyreRemovalListResp{}
	for _, removal := range removals {
		assetIdent := make([]string, 0)

		for _, assetVehicle := range removal.AssetVehicles {
			assetIdent = append(assetIdent, assetVehicle.Asset.GetAssetIdent())
		}

		responseData = append(responseData, VehicleTargetTyreRemovalListResp{
			ID:          removal.ID,
			TargetRTD:   removal.TargetRTD,
			CountAsset:  len(removal.AssetVehicles),
			AssetIdents: assetIdent,
			CreatedAt:   removal.CreatedAt,
			UpdatedAt:   removal.UpdatedAt,
		})
	}

	return responseData
}

type VehicleTargetTyreRemovalDetailResp struct {
	ID        string    `json:"id"`
	TargetRTD float64   `json:"target_rtd"`
	AssetIDs  []string  `json:"asset_ids"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	CreatedBy string    `json:"created_by"`
	UpdatedBy string    `json:"updated_by"`
}

// CheckTargetRTDAvailabilityReq represents the request for checking target RTD availability
type CheckTargetRTDAvailabilityReq struct {
	TargetRTD float64 `json:"target_rtd" binding:"required,min=0" validate:"required,min=0"`
}

// CheckTargetRTDAvailabilityResp represents the response for target RTD availability check
type CheckTargetRTDAvailabilityResp struct {
	ID        null.String `json:"id"`
	Available bool        `json:"available"`
	TargetRTD float64     `json:"target_rtd"`
}
