package dtos

import (
	"assetfindr/pkg/common/commonmodel"
	"strings"
	"time"
)

type Location struct {
	ID                  string       `json:"id"`
	Name                string       `json:"name"`
	IsInventoryLocation bool         `json:"is_inventory_location"`
	Address             string       `json:"address"`
	Floor               string       `json:"floor"`
	Unit                string       `json:"unit"`
	StatusCode          string       `json:"status_code"`
	PICUserID           string       `json:"pic_user_id"`
	PICUser             LocationUser `json:"pic_user"`
	Description         string       `json:"description"`
	MapLat              float64      `json:"map_lat"`
	MapLong             float64      `json:"map_long"`
	CreatedAt           time.Time    `json:"created_at"`
	LocationNumber      string       `json:"location_status"`
}

type LocationUser struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	PhoneNumber string `json:"phone_number,omitempty"`
}

type CreateLocation struct {
	Name                string                 `json:"name" binding:"required"`
	IsInventoryLocation bool                   `json:"is_inventory_location"`
	Address             string                 `json:"address" binding:"required"`
	Floor               string                 `json:"floor"`
	Unit                string                 `json:"unit"`
	StatusCode          string                 `json:"status_code" binding:"required"`
	Description         string                 `json:"description"`
	PICUserID           string                 `json:"pic_user_id" binding:"required"`
	MapLat              float64                `json:"map_lat"`
	MapLong             float64                `json:"map_long"`
	Photos              []commonmodel.PhotoReq `json:"photos"`
}

type UpdateLocation struct {
	Name                string                 `json:"name"`
	IsInventoryLocation bool                   `json:"is_inventory_location"`
	Address             string                 `json:"address"`
	Floor               string                 `json:"floor"`
	Unit                string                 `json:"unit"`
	StatusCode          string                 `json:"status_code"`
	Description         string                 `json:"description"`
	PICUserID           string                 `json:"pic_user_id"`
	MapLat              float64                `json:"map_lat"`
	MapLong             float64                `json:"map_long"`
	Photos              []commonmodel.PhotoReq `json:"photos"`
}

type LocationListReq struct {
	commonmodel.ListRequest
	ShowDeleted bool     `form:"show_deleted"`
	StatusCodes []string `form:"status_codes"`
}

func (l *LocationListReq) Normalize() {
	l.ListRequest.Normalize()

	for _, val := range l.StatusCodes {
		if strings.ToLower(val) == "deleted" {
			l.ShowDeleted = true
		}
	}
}
