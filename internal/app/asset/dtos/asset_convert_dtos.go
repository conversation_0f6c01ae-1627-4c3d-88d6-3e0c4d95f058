package dtos

import (
	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type ConvertToOptimax struct {
	AssetID             string       `json:"asset_id"`
	VehicleID           string       `json:"vehicle_id"`
	MeterCalculationKM  null.Bool    `json:"meter_calculation_km"`
	MeterCalculationHM  null.Bool    `json:"meter_calculation_hm"`
	UseTyreOptimax      bool         `json:"use_tyre_optimax"`
	NumberOfTyres       int          `json:"number_of_tyres"`
	NumberOfSpareTyres  int          `json:"number_of_spare_tyres"`
	PartnerOwnerID      string       `json:"partner_owner_id"`
	AxleConfiguration   pgtype.JSONB `json:"axle_configuration"`
	MaxRtdDiffTolerance null.Int     `json:"max_rtd_diff_tolerance"`
}

type ConvertFromOptimax struct {
	AssetID         string `json:"asset_id"`
	DowngradeReason string `json:"downgrade_reason"`
}
