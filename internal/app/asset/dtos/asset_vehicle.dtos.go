package dtos

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"database/sql"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type AssetAssignmentDetailResponse struct {
	CreatedAt                                 time.Time                   `json:"created_at"`
	UpdatedAt                                 time.Time                   `json:"updated_at" `
	AssetID                                   string                      `json:"asset_id"`
	Asset                                     models.Asset                `json:"asset"`
	RegistrationNumber                        string                      `json:"registration_number"`
	AssetVehicleBodyTypeCode                  string                      `json:"vehicle_body_type_code"`
	AssetVehicleBodyType                      models.AssetVehicleBodyType `json:"asset_vehicle_body_type"`
	NumberOfTyres                             null.Int                    `json:"no_of_tyres"`
	EngineModel                               string                      `json:"engine_model"`
	TransmissionModel                         string                      `json:"transmission_model"`
	VrdNumber                                 string                      `json:"vrd_number_stnk"`
	VrdExpiryDate                             time.Time                   `json:"vrd_expiry_date"`
	VrdNumberAssignTo                         string                      `json:"vrd_number_assign_to"`
	VrdNumberAssignToName                     string                      `json:"vrd_number_assign_to_fullname"`
	EngineNumber                              string                      `json:"engine_number"`
	ChassisNumber                             string                      `json:"chassis_number"`
	GpsDeviceImei                             string                      `json:"gps_device_imei"`
	RegistrationCertificateNumber             string                      `json:"registration_certificate_number"`
	RegistrationCertificateAssignTo           string                      `json:"registration_certificate_assign_to"`
	InspectionBookNumber                      string                      `json:"inspection_book_number"`
	InspectionBookNumberAssignTo              string                      `json:"inspection_book_number_assign_to"`
	InspectionBookNumberAssignToName          string                      `json:"inspection_book_number_assign_to_fullname"`
	InspectionBookExpiryDate                  time.Time                   `json:"inspection_book_expiry_date"`
	ClientID                                  string                      `json:"client_id"`
	Cost                                      int                         `json:"cost"`
	CostAsset                                 int                         `json:"cost_asset"`
	CostExpense                               int                         `json:"cost_expense"`
	VehicleKM                                 float64                     `json:"vehicle_km"`
	VehicleHm                                 float64                     `json:"vehicle_hm"`
	UseKilometer                              null.Bool                   `json:"use_kilometer"`
	UseHourmeter                              null.Bool                   `json:"use_hourmeter"`
	NumberOfSpareTyres                        int                         `json:"no_of_spare_tyres"`
	RegistrationCertificateAssignUserFullName string                      `json:"registration_certificate_assign_user_fullname"`
	StatusInactiveTotalTime                   int                         `json:"status_inactive_total_time"`
	StatusInactiveStartTime                   *sql.NullTime               `json:"status_inactive_start_time"`
	ReferenceNumber                           string                      `json:"reference_number"`
	Vehicle                                   VehicleDetailResp           `json:"vehicle"`
	AxleConfiguration                         pgtype.JSONB                `json:"axle_configuration"`
	MaxRtdDiffTolerance                       *null.Int                   `json:"max_rtd_diff_tolerance"`
	Address                                   string                      `json:"address"`

	AssingedToUserID string                  `json:"assigned_to_user_id"`
	AssingedToUser   AssetAssignmentResponse `json:"assigned_to_user"`
}

func BuildAssetAssignmentDetailResponse(
	assetVehicle models.AssetVehicle,
	assetAssignment AssetAssignmentResponse,
) AssetAssignmentDetailResponse {
	if assetVehicle.Vehicle.AxleConfiguration.Status == pgtype.Undefined {
		assetVehicle.Vehicle.AxleConfiguration.Status = pgtype.Null
	}
	return AssetAssignmentDetailResponse{
		CreatedAt:                       assetVehicle.CreatedAt,
		UpdatedAt:                       assetVehicle.CreatedAt,
		AssetID:                         assetVehicle.AssetID,
		Asset:                           assetVehicle.Asset,
		RegistrationNumber:              assetVehicle.RegistrationNumber,
		AssetVehicleBodyTypeCode:        assetVehicle.AssetVehicleBodyTypeID,
		AssetVehicleBodyType:            assetVehicle.AssetVehicleBodyType,
		NumberOfTyres:                   assetVehicle.NumberOfTyres,
		EngineModel:                     assetVehicle.EngineModel,
		TransmissionModel:               assetVehicle.TransmissionModel,
		VrdNumber:                       assetVehicle.VrdNumber,
		VrdExpiryDate:                   assetVehicle.VrdExpiryDate,
		VrdNumberAssignTo:               assetVehicle.VrdNumberAssignTo,
		EngineNumber:                    assetVehicle.EngineNumber,
		ChassisNumber:                   assetVehicle.ChassisNumber,
		GpsDeviceImei:                   assetVehicle.GpsDeviceImei.String,
		RegistrationCertificateNumber:   assetVehicle.RegistrationCertificateNumber,
		RegistrationCertificateAssignTo: assetVehicle.RegistrationCertificateAssignTo,
		InspectionBookNumber:            assetVehicle.InspectionBookNumber,
		InspectionBookNumberAssignTo:    assetVehicle.InspectionBookNumberAssignTo,
		InspectionBookExpiryDate:        assetVehicle.InspectionBookExpiryDate,
		ClientID:                        assetVehicle.ClientID,
		Cost:                            assetVehicle.Asset.Cost,
		VehicleKM:                       assetVehicle.VehicleKM,
		VehicleHm:                       calculationhelpers.Div100(assetVehicle.VehicleHm),
		UseKilometer:                    assetVehicle.UseKilometer,
		UseHourmeter:                    assetVehicle.UseHourmeter,
		NumberOfSpareTyres:              int(assetVehicle.NumberOfSpareTyres.Int64),
		StatusInactiveTotalTime:         assetVehicle.Asset.StatusInactiveTotalTime,
		StatusInactiveStartTime:         assetVehicle.Asset.StatusInactiveStartTime,
		ReferenceNumber:                 assetVehicle.Asset.ReferenceNumber,
		AxleConfiguration:               assetVehicle.AxleConfiguration,
		MaxRtdDiffTolerance:             assetVehicle.MaxRtdDiffTolerance,
		Address:                         assetVehicle.Asset.Address,
		Vehicle: VehicleDetailResp{
			ID:                assetVehicle.VehicleID,
			BrandID:           assetVehicle.Vehicle.BrandID,
			BrandName:         assetVehicle.Vehicle.Brand.BrandName,
			VehicleTypeCode:   assetVehicle.Vehicle.VehicleTypeCode,
			Model:             assetVehicle.Vehicle.VehicleModel,
			EngineModel:       assetVehicle.Vehicle.EngineModel,
			TransmissionModel: assetVehicle.Vehicle.TransmissionModel,
			IsGeneral:         assetVehicle.Vehicle.IsGeneral(),
			VehicleNumber:     assetVehicle.Vehicle.VehicleNumber,
			AxleConfiguration: assetVehicle.Vehicle.AxleConfiguration,
		},

		AssingedToUserID: assetAssignment.UserID,
		AssingedToUser:   assetAssignment,
	}
}

type UpdateVehicleMeterReq struct {
	AssetID   string  `json:"asset_id"`
	VehicleKM int     `json:"vehicle_km"`
	VehicleHm float64 `json:"vehicle_hm"`
}

type AssetVehicleListReq struct {
	commonmodel.ListRequest
	StatusCodes          []string `form:"status_codes"`
	SubCategories        []string `form:"sub_categories"`
	CustomCategories     []string `form:"custom_categories"`
	CustomSubCategories  []string `form:"custom_sub_categories"`
	Models               []string `form:"models"`
	Brands               []string `form:"brands"`
	Locations            []string `form:"locations"`
	CreateOn             string   `form:"create_on"`
	UpdatedStartDate     string   `form:"updated_start_date"`
	UpdatedEndDate       string   `form:"updated_end_date"`
	PartnerOwnerID       string   `form:"partner_owner_id"`
	UseTyreOptimax       bool     `form:"use_tyre_optimax"`
	HasAxleConfiguration bool     `form:"has_axle_configuration"`
	HasLinkedAssetTyre   bool     `form:"has_linked_asset_tyre"`
}

type AssetVehicleStatsHistoryResp struct {
	Datetime       time.Time `json:"datetime"`
	AssetID        string    `json:"asset_id"`
	VehicleKM      *int      `json:"vehicle_km"`
	VehicleHM      *float64  `json:"vehicle_hm"`
	TotalAssetCost *int      `json:"total_asset_cost"`
}
