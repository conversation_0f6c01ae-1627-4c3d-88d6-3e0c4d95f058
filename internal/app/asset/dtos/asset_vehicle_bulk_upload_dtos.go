package dtos

import (
	"assetfindr/pkg/common/helpers"

	"gopkg.in/guregu/null.v4"
)

type BulkUploadAssetVehicleReq struct {
	ReferenceID                           string    `csv:"reference_id"`
	IsSuccess                             bool      `csv:"is_success"`
	FailedReason                          string    `csv:"failed_reason"`
	IsValidToProcess                      bool      `csv:"-"`
	BrandNo                               string    `csv:"brand_no"`
	BrandID                               string    `csv:"-"`
	LocationNo                            string    `csv:"location_no"`
	AssetVehicleBodyTypeNo                string    `csv:"vehicle_body_type_no"`
	AssetVehicleBodyTypeCode              string    `csv:"-"`
	RegistrationCertificateAssignToEmail  string    `csv:"registration_certificate_assign_to_email"`
	RegistrationCertificateAssignToUserID string    `csv:"-"`
	InspectionBookNumberAssignToEmail     string    `csv:"inspection_book_number_assign_to_email"`
	InspectionBookNumberAssignToUserID    string    `csv:"-"`
	AssingedToUserID                      string    `csv:"-"`
	AssingedToUserEmail                   string    `csv:"assigned_to_user_email"`
	Name                                  string    `csv:"name"`
	ModelNumber                           string    `csv:"model_number"`
	SerialNumber                          string    `csv:"serial_number"`
	AssetCategoryCode                     string    `csv:"category_code"`
	SubCategoryCode                       string    `csv:"sub_category_code"`
	ProductionDate                        string    `csv:"production_date"`
	Cost                                  int       `csv:"cost"`
	AssetStatusCode                       string    `csv:"status_code"`
	OwnershipCategoryCode                 string    `csv:"ownership_category_code"`
	RegistrationNumber                    string    `csv:"registration_number"`
	ProductionYear                        int       `csv:"production_year"`
	NumberOfTyres                         int       `csv:"no_of_tyres"`
	EngineModel                           string    `csv:"engine_model"`
	TransmissionModel                     string    `csv:"transmission_model"`
	VrdNumber                             string    `csv:"vrd_number_stnk"`
	VrdExpiryDate                         string    `csv:"vrd_expiry_date"`
	EngineNumber                          string    `csv:"engine_number"`
	ChassisNumber                         string    `csv:"chassis_number"`
	GpsDeviceImei                         string    `csv:"gps_device_imei"`
	RegistrationCertificateNumber         string    `csv:"registration_certificate_number"`
	InspectionBookNumber                  string    `csv:"inspection_book_number"`
	InspectionBookExpiryDate              string    `csv:"inspection_book_expiry_date"`
	VehicleKM                             float64   `csv:"vehicle_km"`
	VehicleHm                             float64   `csv:"vehicle_hm"`
	NumberOfSpareTyres                    int       `csv:"no_of_spare_tyres"`
	UseKilometer                          null.Bool `csv:"use_kilometer"`
	UseHourmeter                          null.Bool `csv:"use_hourmeter"`
	UseKilometerHourmeterValidation       string    `csv:"use_kilometer_hourmeter_validation"`
}

func (r *BulkUploadAssetVehicleReq) ValidateUseKilometerHourmeter() {
	if !r.UseHourmeter.Bool && !r.UseKilometer.Bool {
		r.UseKilometerHourmeterValidation = "one on use_kilometer or use_hourmeter must be true"
		r.IsValidToProcess = false
	}

	r.UseKilometerHourmeterValidation = ""
}

func (r *BulkUploadAssetVehicleReq) ValidateRegistrationCertificateAssignToEmail(mapData map[string]string) {
	id, ok := mapData[r.RegistrationCertificateAssignToEmail]
	if !ok {
		r.RegistrationCertificateAssignToEmail = helpers.AddSuffixInvalid(r.RegistrationCertificateAssignToEmail)
		r.IsValidToProcess = false
	} else {
		r.RegistrationCertificateAssignToUserID = id
	}
}

func (r *BulkUploadAssetVehicleReq) ValidateInspectionBookNumberAssignToEmail(mapData map[string]string) {
	id, ok := mapData[r.InspectionBookNumberAssignToEmail]
	if !ok {
		r.InspectionBookNumberAssignToEmail = helpers.AddSuffixInvalid(r.InspectionBookNumberAssignToEmail)
		r.IsValidToProcess = false
	} else {
		r.InspectionBookNumberAssignToUserID = id
	}
}

func (r *BulkUploadAssetVehicleReq) ValidateAssingedToUserEmail(mapData map[string]string) {
	id, ok := mapData[r.AssingedToUserEmail]
	if !ok {
		r.AssingedToUserEmail = helpers.AddSuffixInvalid(r.AssingedToUserEmail)
		r.IsValidToProcess = false
	} else {
		r.AssingedToUserID = id
	}
}

func (r *BulkUploadAssetVehicleReq) ValidateAssetVehicleBodyTypeNo(mapData map[string]string) {
	code, ok := mapData[r.AssetVehicleBodyTypeNo]
	if !ok {
		r.AssetVehicleBodyTypeNo = helpers.AddSuffixInvalid(r.AssetVehicleBodyTypeNo)
		r.IsValidToProcess = false
	} else {
		r.AssetVehicleBodyTypeCode = code
	}
}

func (r *BulkUploadAssetVehicleReq) ValidateBrandNo(mapData map[string]string) {
	code, ok := mapData[r.BrandNo]
	if !ok {
		r.BrandNo = helpers.AddSuffixInvalid(r.BrandNo)
		r.IsValidToProcess = false
	} else {
		r.BrandID = code
	}
}

func (r *BulkUploadAssetVehicleReq) ValidateEngineNumber(mapData map[string]bool) {
	if mapData[r.EngineNumber] {
		r.IsValidToProcess = false
		r.EngineNumber = helpers.AddSuffixDuplicate(r.EngineNumber)
		return
	}

	mapData[r.EngineNumber] = true
}

func (r *BulkUploadAssetVehicleReq) ValidateRegistrationNumber(mapData map[string]bool) {
	if mapData[r.RegistrationNumber] {
		r.IsValidToProcess = false
		r.RegistrationNumber = helpers.AddSuffixDuplicate(r.RegistrationNumber)
		return
	}

	mapData[r.RegistrationNumber] = true
}
