package dtos

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/calculationhelpers"

	"time"

	"gopkg.in/guregu/null.v4"
)

type AssetTyrePosition struct {
	AssetLinkedID string
	ChildAssetID  string
	ParentAssetID string
	TyrePosition  int
}

type ResponseAssetTyreById struct {
	AssetTyre    *models.AssetTyre `json:"asset_tyre"`
	TyrePosition int               `json:"tyre_position"`
	TUR          float64           `json:"utilization_rate_percentage"`
}

type AssetListTyreItemResponse struct {
	ID                              string                  `json:"id"`
	SerialNumber                    string                  `json:"serial_number"`
	BrandID                         string                  `json:"brand_id"`
	BrandName                       string                  `json:"brand_name" `
	CreatedAt                       time.Time               `json:"created_at"`
	UpdatedAt                       time.Time               `json:"updated_at" `
	Name                            string                  `json:"name"`
	PatternType                     string                  `json:"pattern_type"`
	TyrePosition                    int                     `json:"tyre_position"`
	PlyRating                       float64                 `json:"ply_rating"`
	DateCode                        string                  `json:"date_code"`
	AssingedToUserID                string                  `json:"assigned_to_user_id"`
	AssingedToUser                  AssetAssignmentResponse `json:"assigned_to_user"`
	AttachedAssetWithAssetName      string                  `json:"attached_asset_with_asset_name"`
	DatetimeOfLastCheck             time.Time               `json:"datetime_of_last_check"`
	AverageRTD                      float64                 `json:"average_rtd"`
	Rtd1                            float64                 `json:"rtd1"`
	Rtd2                            float64                 `json:"rtd2"`
	Rtd3                            float64                 `json:"rtd3"`
	Rtd4                            float64                 `json:"rtd4"`
	UtilizationRatePercentage       float64                 `json:"utilization_rate_percentage"`
	TotalKM                         int                     `json:"total_km"`
	TotalHm                         float64                 `json:"total_hm"`
	ProjectedLifeKM                 int                     `json:"projected_life_km"`
	ProjectedLifeHm                 float64                 `json:"projected_life_hm"`
	StatusCode                      string                  `json:"status_code"`
	Status                          string                  `json:"status"`
	Tyre                            models.Tyre             `json:"tyre"`
	StartThreadDepth                float64                 `json:"start_thread_depth"`
	SectionWidth                    string                  `json:"section_width"`
	Construction                    string                  `json:"construction"`
	RimDiameter                     string                  `json:"rim_diameter"`
	RecommendedRimSize              string                  `json:"recommended_rim_size"`
	Rfid                            string                  `json:"rfid"`
	AssetLinkedID                   string                  `json:"asset_linked_id,omitempty"`
	MeterCalculationCode            null.String             `json:"meter_calculation_code" `
	AverageRtdLastUpdatedAt         null.Time               `json:"average_rtd_last_updated_at"`
	Pressure                        null.Float              `json:"pressure"`
	PressureLastUpdatedAt           null.Time               `json:"pressure_last_updated_at"`
	Temperature                     null.Float              `json:"temperature"`
	TemperatureLastUpdatedAt        null.Time               `json:"temperature_last_updated_at"`
	PressureLastUpdatedSensorRef    string                  `json:"pressure_last_updated_sensor_ref"`
	TemperatureLastUpdatedSensorRef string                  `json:"temperature_last_updated_sensor_ref"`
	HasNotSetRTD                    null.Bool               `json:"has_not_set_rtd"`

	UtilizationRatePercentageStatusCode string     `json:"utilization_rate_percentage_status_code"`
	IsSpare                             *null.Bool `json:"is_spare"`
}

type AssetTyreItemReceiver struct {
	ID               string  `json:"id"`
	AssetID          string  `json:"asset_id"`
	PatternType      string  `json:"pattern_type"`
	TyrePosition     int     `json:"tyre_position"`
	PlyRating        float64 `json:"ply_rating"`
	AverageRTD       float64 `json:"average_rtd"`
	TotalKM          int     `json:"total_km"`
	TotalHm          float64 `json:"total_hm"`
	Pressure         float64 `json:"pressure"`
	RetreadNumber    int     `json:"retread_number"`
	ConstructionType string  `json:"construction_type"`
	LoadRating       string  `json:"load_rating"`
	SpeedIndex       int     `json:"speed_index"`
	TRACode          string  `json:"tra_code"`
	StarRating       float64 `json:"star_rating"`
	DOTCode          string  `json:"dot_code"`
	DateCode         string  `json:"date_code"`
	ClientID         string  `json:"client_id"`
	SerialNumber     string  `json:"serial_number"`
	BrandID          string  `json:"brand_id"`
	BrandName        string  `json:"brand_name" `
	Name             string  `json:"name"`
	AssingedToUserID string  `json:"assigned_to_user_id"`
	StatusCode       string  `json:"status_code"`
	Status           string  `json:"status"`
	CategoryCode     string  `json:"category_code"`
	TyreID           string  `json:"tyre_id"`
	Cost             int     `json:"cost"`
	StartThreadDepth float64 `json:"start_thread_depth"`
	SectionWidth     string  `json:"section_width"`
	Construction     string  `json:"construction"`
	RimDiameter      string  `json:"rim_diameter"`
	Rfid             string  `json:"rfid"`

	Photos []commonmodel.PhotoReq `json:"photos"`
}

func BuildAssetTyreListResponse(
	assetTyres []models.AssetTyre,
	assignmentsMapByAssetId map[string]AssetAssignmentResponse,
	linkedAssetMapByChildAssetIds map[string]models.AssetLinked,
	assetTyrePosition []AssetTyrePosition,
) []AssetListTyreItemResponse {
	response := []AssetListTyreItemResponse{}

	for _, assetTyre := range assetTyres {
		var tyrePositions int
		var assetLinkedID string
		for _, tyrePosition := range assetTyrePosition {
			if assetTyre.AssetID == tyrePosition.ChildAssetID {
				tyrePositions = tyrePosition.TyrePosition
				assetLinkedID = tyrePosition.AssetLinkedID
			}
		}

		assingedToUserID := ""
		assetAssignment, assetAssignmentExists := assignmentsMapByAssetId[assetTyre.AssetID]
		if assetAssignmentExists {
			assingedToUserID = assetAssignment.UserID
		}

		attachedAssetWithAssetName := ""
		attachedAsset, attachedAssetExists := linkedAssetMapByChildAssetIds[assetTyre.AssetID]
		if attachedAssetExists {
			attachedAssetWithAssetName = attachedAsset.ParentAsset.RegistrationNumber
		}
		tUR := assetTyre.UtilizationRatePercentage
		projectedLifeKM := assetTyre.ProjectedLifeKM()
		projectedLifeHm := assetTyre.ProjectedLifeHm()

		response = append(response, AssetListTyreItemResponse{
			ID:                              assetTyre.AssetID,
			SerialNumber:                    assetTyre.Asset.SerialNumber,
			BrandID:                         assetTyre.Asset.BrandID,
			BrandName:                       assetTyre.Asset.Brand.BrandName,
			CreatedAt:                       assetTyre.CreatedAt,
			UpdatedAt:                       assetTyre.UpdatedAt,
			Name:                            assetTyre.Asset.Name,
			DateCode:                        assetTyre.DateCode.String,
			DatetimeOfLastCheck:             assetTyre.DatetimeOfLastCheck,
			Pressure:                        assetTyre.Pressure,
			AverageRTD:                      assetTyre.AverageRTD,
			Rtd1:                            assetTyre.Rtd1,
			Rtd2:                            assetTyre.Rtd2,
			Rtd3:                            assetTyre.Rtd3,
			Rtd4:                            assetTyre.Rtd4,
			UtilizationRatePercentage:       tUR,
			TotalKM:                         assetTyre.TotalKM,
			TotalHm:                         calculationhelpers.Div100(assetTyre.TotalHm),
			ProjectedLifeKM:                 projectedLifeKM,
			ProjectedLifeHm:                 projectedLifeHm,
			StatusCode:                      assetTyre.Asset.AssetStatusCode,
			Status:                          assetTyre.Asset.AssetStatus.Label,
			AssingedToUserID:                assingedToUserID,
			AssingedToUser:                  assetAssignment,
			Tyre:                            assetTyre.Tyre,
			TyrePosition:                    tyrePositions,
			AttachedAssetWithAssetName:      attachedAssetWithAssetName,
			StartThreadDepth:                assetTyre.StartThreadDepth,
			Rfid:                            assetTyre.Asset.Rfid,
			AssetLinkedID:                   assetLinkedID,
			MeterCalculationCode:            assetTyre.MeterCalculationCode,
			AverageRtdLastUpdatedAt:         assetTyre.AverageRtdLastUpdatedAt,
			PressureLastUpdatedAt:           assetTyre.PressureLastUpdatedAt,
			Temperature:                     assetTyre.Temperature,
			TemperatureLastUpdatedAt:        assetTyre.TemperatureLastUpdatedAt,
			PressureLastUpdatedSensorRef:    assetTyre.PressureLastUpdatedSensorRef,
			TemperatureLastUpdatedSensorRef: assetTyre.TemperatureLastUpdatedSensorRef,
			HasNotSetRTD:                    assetTyre.HasNotSetRTD,

			UtilizationRatePercentageStatusCode: assetTyre.UtilizationRatePercentageStatusCode,
			IsSpare:                             assetTyre.IsSpare,
		})
	}

	return response
}

type CreateAssetTyreVehicleResponse struct {
	Asset   AssetTyreItemReceiver `json:"asset"`
	Message string                `json:"message"`
}

type GetAssetTyresRequest struct {
	commonmodel.ListRequest
	IsUnlinkTyre      bool      `form:"is_unlink_tyre"`
	StatusCodes       []string  `form:"status_codes"`
	BrandIDs          []string  `form:"brand_ids"`
	AssignedUserIDs   []string  `form:"assigned_user_ids"`
	TyreSizes         []string  `form:"tyre_sizes"`
	Rfids             []string  `form:"rfids"`
	HasPartnerOwnerID null.Bool `form:"has_partner_owner_id"`
	PartnerOwnerID    string    `form:"partner_owner_id"`
	IsWorkshop        null.Bool `form:"is_workshop"`
	TURStatusCodes    []string  `form:"tur_status_codes"`

	LastInspectedBeforeDate time.Time `form:"last_inspected_before_date"`

	MeterCalculationCode string `form:"meter_calculation_code"`
	InitialConditionCode string `form:"initial_condition_code"`

	IsCriticalOnLinkedNonSpare bool `form:"is_critical_on_linked_non_spare"`
}

type UpdateAssetTyreParam struct {
	Req           AssetTyreItemReceiver
	LoginClientID string
	AssetID       string
}

type Asset struct {
	ID              string      `json:"id"`
	CreatedAt       time.Time   `json:"created_at"`
	UpdatedAt       time.Time   `json:"updated_at"`
	DeletedAt       interface{} `json:"deleted_at"`
	Name            string      `json:"name"`
	BrandID         string      `json:"brand_id"`
	Brand           Brand       `json:"brand"`
	ModelNumber     string      `json:"model_number"`
	SerialNumber    string      `json:"serial_number"`
	ReferenceNumber string      `json:"reference_number"`
	CategoryCode    string      `json:"category_code"`
	ProductionDate  time.Time   `json:"production_date"`
	ClientID        string      `json:"client_id"`
	Cost            int         `json:"cost"`
	StatusCode      string      `json:"status_code"`
	Rfid            string      `json:"rfid"`

	InitialConditionCode string `json:"initial_condition_code"`
}

func BuildAssetResp(asset *models.Asset) *Asset {
	if asset == nil {
		return nil
	}

	return &Asset{
		ID:        asset.ID,
		CreatedAt: asset.CreatedAt,
		UpdatedAt: asset.UpdatedAt,
		DeletedAt: asset.DeletedAt,
		Name:      asset.Name,
		BrandID:   asset.BrandID,
		Brand: Brand{
			ID:        asset.BrandID,
			BrandName: asset.Brand.BrandName,
		},
		ModelNumber:          asset.ModelNumber,
		SerialNumber:         asset.SerialNumber,
		ReferenceNumber:      asset.ReferenceNumber,
		CategoryCode:         asset.AssetCategoryCode,
		ProductionDate:       asset.ProductionDate,
		ClientID:             asset.ClientID,
		Cost:                 asset.Cost,
		StatusCode:           asset.AssetStatusCode,
		Rfid:                 asset.Rfid,
		InitialConditionCode: asset.InitialConditionCode,
	}
}

type Tyre struct {
	ID                 string    `json:"id"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	BrandID            string    `json:"brand_id"`
	PatternType        string    `json:"pattern_type"`
	OriginalTd         float64   `json:"original_td"`
	ConstructionType   string    `json:"construction_type"`
	PlyRating          float64   `json:"ply_rating"`
	LoadRating         string    `json:"load_rating"`
	SpeedIndex         string    `json:"speed_index"`
	StarRating         float64   `json:"star_rating"`
	TraCode            string    `json:"tra_code"`
	ClientID           string    `json:"client_id"`
	SectionWidth       string    `json:"section_width"`
	Construction       string    `json:"construction"`
	RimDiameter        string    `json:"rim_diameter"`
	RecommendedRimSize string    `json:"recommended_rim_size"`
}

func BuildTyreResp(tyreModel models.Tyre) Tyre {
	return Tyre{
		ID:                 tyreModel.ID,
		CreatedAt:          tyreModel.CreatedAt,
		UpdatedAt:          tyreModel.UpdatedAt,
		BrandID:            tyreModel.BrandID,
		PatternType:        tyreModel.PatternType,
		OriginalTd:         tyreModel.OriginalTd,
		ConstructionType:   tyreModel.ConstructionType,
		PlyRating:          tyreModel.PlyRating,
		LoadRating:         tyreModel.LoadRating,
		SpeedIndex:         tyreModel.SpeedIndex,
		StarRating:         tyreModel.StarRating,
		TraCode:            tyreModel.TRACode,
		ClientID:           tyreModel.ClientID,
		SectionWidth:       tyreModel.SectionWidth,
		Construction:       tyreModel.Construction,
		RimDiameter:        tyreModel.RimDiameter,
		RecommendedRimSize: tyreModel.RecommendedRimSize,
	}
}

type AssetTyreDetail struct {
	CreatedAt                       time.Time                        `json:"created_at"`
	UpdatedAt                       time.Time                        `json:"updated_at"`
	AssetID                         string                           `json:"asset_id"`
	Asset                           Asset                            `json:"asset"`
	DatetimeOfLastCheck             time.Time                        `json:"datetime_of_last_check"`
	AverageRTD                      float64                          `json:"average_rtd"`
	UtilizationRatePercentage       float64                          `json:"utilization_rate_percentage"`
	TotalKm                         int                              `json:"total_km"`
	TotalHm                         float64                          `json:"total_hm"`
	ProjectedLifeKM                 int                              `json:"projected_life_km"`
	ProjectedLifeHm                 float64                          `json:"projected_life_hm"`
	RetreadNumber                   int                              `json:"retread_number"`
	RepairedNumber                  int                              `json:"repaired_number"`
	LifetimeKM                      int                              `json:"lifetime_km"`
	LifetimeCPK                     float64                          `json:"lifetime_cpk"`
	LifetimeHM                      float64                          `json:"lifetime_hm"`
	LifetimeCPH                     float64                          `json:"lifetime_cph"`
	DotCode                         string                           `json:"dot_code"`
	DateCode                        string                           `json:"date_code"`
	ClientID                        string                           `json:"client_id"`
	TyreID                          string                           `json:"tyre_id"`
	StartThreadDepth                float64                          `json:"start_thread_depth"`
	OriginalTd                      float64                          `json:"original_td"`
	MeterCalculationCode            null.String                      `json:"meter_calculation_code" `
	AverageRtdLastUpdatedAt         null.Time                        `json:"average_rtd_last_updated_at"`
	Pressure                        null.Float                       `json:"pressure"`
	PressureLastUpdatedAt           null.Time                        `json:"pressure_last_updated_at"`
	Temperature                     null.Float                       `json:"temperature"`
	TemperatureLastUpdatedAt        null.Time                        `json:"temperature_last_updated_at"`
	PressureLastUpdatedSensorRef    string                           `json:"pressure_last_updated_sensor_ref"`
	TemperatureLastUpdatedSensorRef string                           `json:"temperature_last_updated_sensor_ref"`
	TotalLifetime                   int                              `json:"total_lifetime"`
	PartnerOwnerID                  string                           `json:"partner_owner_id"`
	PartnerOwnerNo                  string                           `json:"partner_owner_no"`
	PartnerOwnerName                string                           `json:"partner_owner_name"`
	Tyre                            Tyre                             `json:"tyre"`
	AssetVehicleLinked              *AssetVehicleLinked              `json:"asset_vehicle_linked"`
	Cost                            int                              `json:"cost"`
	CostAsset                       int                              `json:"cost_asset"`
	CostExpense                     int                              `json:"cost_expense"`
	HasNotSetRTD                    null.Bool                        `json:"has_not_set_rtd"`
	IsWorkshop                      bool                             `json:"is_workshop"`
	PrevKmHmDataUnavailable         null.Bool                        `json:"prev_km_hm_data_unavailable"`
	PrevTotalKM                     null.Int                         `json:"prev_total_km"`
	PrevTotalHm                     null.Float                       `json:"prev_total_hm"`
	IsSpare                         *null.Bool                       `json:"is_spare"`
	RetreadTyre                     []RetreadAssetTyreRes            `json:"retread_tyre"`
	LastInspectionTyre              *GetAssetInspectionTyresResponse `json:"last_inspection_tyre"`

	LastInspectionTyreTreadDepth  *GetAssetInspectionTyresResponse `json:"last_inspection_tyre_tread_depth"`
	LastInspectionTyreTemperature *GetAssetInspectionTyresResponse `json:"last_inspection_tyre_temperature"`
	LastInspectionTyrePressure    *GetAssetInspectionTyresResponse `json:"last_inspection_tyre_pressure"`
}

type AssetVehicleLinked struct {
	ID                 string          `json:"id"`
	ReferenceNumber    string          `json:"reference_number"`
	SerialNumber       string          `json:"serial_number"`
	RegistrationNumber string          `json:"registration_number"`
	VehicleBodyType    VehicleBodyType `json:"vehicle_body_type"`
	Name               string          `json:"name"`
	LinkedDatetime     time.Time       `json:"linked_datetime" gorm:"not null"`
	TyrePosition       int             `json:"tyre_position"`
}

type VehicleBodyType struct {
	Code        string `json:"code"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

type AssetTyreListItemResp struct {
	ID                                    string                    `json:"id"`
	Name                                  string                    `json:"name"`
	SerialNumber                          string                    `json:"serial_number"`
	AssetStatusCode                       string                    `json:"asset_status_code"`
	AssetStatus                           commonmodel.ConstantModel `json:"asset_status"`
	PartnerOwnerID                        string                    `json:"partner_owner_id"`
	PartnerOwnerNo                        string                    `json:"partner_owner_no"`
	PartnerOwnerName                      string                    `json:"partner_owner_name"`
	BrandID                               string                    `json:"brand_id"`
	BrandName                             string                    `json:"brand_name"`
	DateCode                              string                    `json:"date_code"`
	DOTCode                               string                    `json:"dot_code"`
	TyreID                                string                    `json:"tyre_id"`
	Tyre                                  Tyre                      `json:"tyre"`
	TyrePosition                          int                       `json:"tyre_position"`
	AttachedAssetWithAssetID              string                    `json:"attached_asset_with_asset_id"`
	AttachedAssetWithAssetReferenceNumber string                    `json:"attached_asset_with_asset_reference_number"`
	AttachedAssetWithAssetSerialNumber    string                    `json:"attached_asset_with_asset_serial_number"`
	AssingedToUserID                      string                    `json:"assinged_to_user_id"`
	AssingedToUserName                    string                    `json:"assinged_to_user_name"`
	Rfid                                  string                    `json:"rfid"`
	MeterCalculationCode                  null.String               `json:"meter_calculation_code"`
	TreadNumber                           int                       `json:"tread_number"`
	InitialConditionCode                  string                    `json:"initial_condition_code"`

	DatetimeOfLastCheck       null.Time `json:"datetime_of_last_check"`
	LastInspectedAt           null.Time `json:"last_inspected_at"`
	AverageRTD                float64   `json:"average_rtd"`
	TotalKM                   int       `json:"total_km"`
	TotalHm                   float64   `json:"total_hm"`
	LifetimeKM                int       `json:"lifetime_km"`
	LifetimeHm                float64   `json:"lifetime_hm"`
	TUR                       float64   `json:"utilization_rate_percentage"`
	UtilizationRateStatusCode string    `json:"utilization_rate_status_code"`
	ProjectedLifeKM           int       `json:"projected_life_km"`
	ProjectedLifeHm           float64   `json:"projected_life_hm"`

	// RetreadNumber       int       `json:"retread_number"`
	// DOTCode             string    `json:"dot_code"`
	StartThreadDepth float64 `json:"start_thread_depth"`
	OriginalTd       float64 `json:"original_td"`
	// TotalLifetime       int       `json:"total_lifetime"`
	IsWorkshop   bool       `json:"is_workshop"`
	HasNotSetRTD null.Bool  `json:"has_not_set_rtd"`
	IsSpare      *null.Bool `json:"is_spare"`
}

type AssetTyreStatsHistoryResp struct {
	Datetime       time.Time `json:"datetime"`
	AssetID        string    `json:"asset_id"`
	TotalKM        int       `json:"total_km"`
	TotalCPK       *float64  `json:"total_cpk"`
	TotalHM        float64   `json:"total_hm"`
	TotalCPH       *float64  `json:"total_cph"`
	TotalLifetime  int       `json:"total_lifetime"`
	TotalAssetCost *int      `json:"total_asset_cost"`
}
