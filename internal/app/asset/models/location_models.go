package models

import (
	"assetfindr/pkg/common/commonmodel"
	"database/sql"

	"gorm.io/gorm"
)

type Location struct {
	commonmodel.ModelV2
	Name                string
	IsInventoryLocation sql.NullBool
	Address             string
	Floor               sql.NullString `gorm:"default:null"`
	Unit                sql.NullString `gorm:"default:null"`
	StatusCode          string
	Description         sql.NullString `gorm:"not null"`
	PICUserID           string         `gorm:"column:pic_user_id;not null"`
	MapLat              float64
	MapLong             float64
	LocationNumber      string
}

func (l Location) TableName() string {
	return "ams_locations"
}

func (l *Location) BeforeCreate(db *gorm.DB) error {
	l.SetUUID("loc")
	l.ModelV2.BeforeCreate(db)
	return nil
}

func (l *Location) BeforeUpdate(db *gorm.DB) error {
	l.ModelV2.BeforeUpdate(db)
	return nil
}

type LocationStatus struct {
	Code        string `gorm:"primaryKey"`
	Label       string
	Description string
}

func (ls LocationStatus) TableName() string {
	return "ams_LOCATION_STATUSES"
}

type LocationWhere struct {
	ID             string
	ClientID       string
	ShowDeleted    bool
	WithOrmDeleted bool
	StatusCodes    []string
}

type LocationCondition struct {
	Where       LocationWhere
	Columns     []string
	IsForUpdate bool
}

type GetLocationListParam struct {
	commonmodel.ListRequest
	Cond LocationCondition
}
