package models

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/pkg/common/commonmodel"
	"database/sql"
	"fmt"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type Asset struct {
	commonmodel.ModelV2
	Name                    string                 `json:"name" gorm:"type:varchar(100);default:null" pgjsonb:"name"`
	BrandID                 string                 `json:"brand_id" gorm:"type:varchar(40);default:null" pgjsonb:"brand_id"`
	Brand                   Brand                  `json:"brand" pgjsonb:"brand"`
	ModelNumber             string                 `json:"model_number" gorm:"type:varchar(50)" pgjsonb:"model_number"`
	SerialNumber            string                 `json:"serial_number" gorm:"type:varchar(50)" pgjsonb:"serial_number"`
	AssetCategoryCode       string                 `json:"category_code" gorm:"type:varchar(20);not null" pgjsonb:"asset_category_code"`
	AssetCategory           AssetCategory          `json:"category" pgjsonb:"asset_category"`
	SubCategoryCode         string                 `json:"sub_category_code" pgjsonb:"sub_category_code"`
	SubCategory             AssetSubCategory       `json:"sub_category" pgjsonb:"sub_category"`
	ProductionDate          time.Time              `json:"production_date" gorm:"default:null" pgjsonb:"production_date"`
	Cost                    int                    `json:"cost" pgjsonb:"cost"`
	AssetStatusCode         string                 `json:"status_code" gorm:"type:varchar(20);not null" pgjsonb:"asset_status_code"`
	OwnershipCategoryCode   string                 `json:"ownership_category_code" gorm:"type:varchar(20);not null" pgjsonb:"ownership_category_code"`
	OwnershipCategory       AssetOwnershipCategory `json:"ownership_category" pgjsonb:"ownership_category"`
	LocationID              string                 `json:"location_id" gorm:"type:varchar(40);default:null" pgjsonb:"location_id"`
	Location                Location               `json:"location" pgjsonb:"location"`
	Photo                   null.String            `json:"photo" gorm:"type:varchar(255);default:null" pgjsonb:"photo"`
	StatusInactiveTotalTime int                    `json:"status_inactive_total_time" pgjsonb:"status_inactive_total_time"`
	StatusInactiveStartTime *sql.NullTime          `json:"status_inactive_start_time" pgjsonb:"status_inactive_start_time"`
	ReferenceNumber         string                 `json:"reference_number" pgjsonb:"reference_number"`
	PartnerOwnerID          string                 `json:"partner_owner_id" pgjsonb:"partner_owner_id" gorm:"default:null"`
	PartnerOwnerNo          string                 `json:"partner_owner_no" pgjsonb:"partner_owner_no" gorm:"default:null"`
	PartnerOwnerName        string                 `json:"partner_owner_name" pgjsonb:"partner_owner_name" gorm:"default:null"`
	Rfid                    string                 `json:"rfid" gorm:"default:null" pgjsonb:"rfid"`
	InitialConditionCode    string                 `json:"initial_condition_code" gorm:"default:null" pgjsonb:"condition_code"`
	GpsImei                 string                 `json:"gps_imei" gorm:"default:null;column:gps_imei" pgjsonb:"gps_imei"`
	HandoverFormTemplateID  null.String            `gorm:"default:null" json:"handover_form_template_id" pgjsonb:"handover_form_template_id"`
	HandoverNeedInspection  null.Bool              `gorm:"default:null" json:"handover_need_inspection" pgjsonb:"handover_need_inspection"`
	Address                 string                 `gorm:"type:varchar(255);default:null" json:"address"  pgjsonb:"address"`
	InitialCondition        AssetInitialCondition  `json:"initial_condition" gorm:"foreignKey:InitialConditionCode;references:Code" pgjsonb:"condition"`

	AddressMapLink           string `gorm:"type:varchar(255);default:null" json:"address_map_link" pgjsonb:"address_map_link"`
	UsingPartnerOwnerAddress bool   `gorm:"default:false" json:"using_partner_owner_address" pgjsonb:"using_partner_owner_address"`

	CustomAssetCategoryID    string                 `json:"custom_asset_category_id" pgjsonb:"custom_asset_category_id" gorm:"type:varchar(40);default:null"`
	CustomAssetCategory      CustomAssetCategory    `json:"custom_category" pgjsonb:"custom_asset_category"`
	CustomAssetSubCategoryID *null.String           `json:"custom_asset_sub_category_id" pgjsonb:"custom_asset_sub_category_id" gorm:"type:varchar(40);default:null"`
	CustomAssetSubCategory   CustomAssetSubCategory `json:"custom_sub_category" pgjsonb:"custom_asset_sub_category"`

	UseTyreOptimax  null.Bool `json:"use_tyre_optimax" pgjsonb:"use_tyre_optimax" gorm:"default:false"`
	UseFleetOptimax null.Bool `json:"use_fleet_optimax" pgjsonb:"use_fleet_optimax" gorm:"default:false"`
	DowngradeReason string    `json:"downgrade_reason" pgjson:"downgrade_reason" gorm:"default:null"`

	DowngradeTyreOptimaxReason null.String `json:"downgrade_tyre_optimax_reason" pgjson:"downgrade_tyre_optimax_reason" gorm:"default:null"`

	ModelID    *null.String `json:"model_id" pgjsonb:"model_id" gorm:"type:varchar(40);default:null"`
	IsWorkshop bool         `json:"is_workshop" gorm:"default:null"`

	AssetStatus AssetStatus `json:"asset_status"`

	AssetVehicle     *AssetVehicle    `json:"-"`
	AssetTyre        *AssetTyre       `json:"-"`
	ChildAssetLinked *AssetLinked     `json:"-" gorm:"foreignKey:ID;references:ChildAssetID"`
	AssetAssignment  *AssetAssignment `json:"asset_assignment" gorm:"foreignKey:ID;references:AssetID"`
	AssetModel       AssetModel       `json:"models" gorm:"foreignKey:model_id;reference:id"`
}

type AssetForUpdateWithNull struct {
	Asset
	BrandID         *null.String
	SerialNumber    null.String
	ReferenceNumber null.String
	Rfid            null.String
}

// Deprecated: Please dont use for futher dev
func (a Asset) StatusLabel() string {
	return constants.MapAssetStatusLabel[a.AssetStatusCode]
}

// TableName specifies the database table name for the AmsAsset model
func (a *Asset) TableName() string {
	return "ams_assets"
}

func (a *Asset) BeforeCreate(db *gorm.DB) error {
	a.SetUUID("ass")
	a.ModelV2.BeforeCreate(db)
	return nil
}

func (a *Asset) BeforeUpdate(db *gorm.DB) error {
	a.ModelV2.BeforeUpdate(db)
	return nil
}

type AssetWhere struct {
	ID               string
	ExcludedID       string
	IDs              []string
	ClientID         string
	ClientIDs        []string
	CreatedBy        string
	AssignUserID     string
	AssetIds         []string
	SerialNumbers    []string
	ReferenceNumbers []string
	Rfid             string
	Rfids            []string
	Statuses         []string
	ExcludedStatuses []string
	WithOrmDeleted   bool
	PartnerOwnerID   string
	Categories       []string
	SubCategories    []string
	LocationIds      []string
	CreateOn         string
	CreatedStartDate string
	CreatedEndDate   string
	LocationID       string
	BrandID          string

	CustomAssetCategoryID     string
	CustomAssetCategoryIDs    []string
	CustomAssetSubCategoryIDs []string

	LowerSerialNumber  string
	LowerRfid          string
	ExcludedCategories []string
	IsWorkshop         bool

	IsOverdue       bool
	OptimaxStatuses []string

	NotLinkedToAssetID string

	NonViewAllAssetCondition *NonViewAllAssetCondition
	ModelID                  string
	ModelIDs                 []string
}

type NonViewAllAssetCondition struct {
	UserID string
}

type AssetCondition struct {
	Where   AssetWhere
	Columns []string
	Preload AssetPreload
}

type AssetPreload struct {
	Brand                  bool
	AssetCategory          bool
	SubCategory            bool
	CustomAssetCategory    bool
	CustomAssetSubCategory bool
	OwnershipCategory      bool
	Location               bool
	AssetStatus            bool
	AssetTyre              bool
	AssetAssignment        bool
	AssetModel             bool

	AssetTyreWithLinkedParent bool
	AssetTyreTyre             bool
}

type GetAssetListParam struct {
	commonmodel.ListRequest
	Cond AssetCondition
}

func (a *Asset) GetAssetIdentForNotif() string {
	assetIdent := a.ReferenceNumber
	if assetIdent == "" {
		assetIdent = a.SerialNumber
	} else if a.SerialNumber != "" {
		assetIdent = fmt.Sprintf("%s/%s", a.ReferenceNumber, a.SerialNumber)
	}

	if a.Name != "" {
		return fmt.Sprintf("%s - %s", a.Name, assetIdent)
	}

	return assetIdent
}
