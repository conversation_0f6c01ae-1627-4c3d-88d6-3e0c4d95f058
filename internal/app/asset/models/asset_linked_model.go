package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gorm.io/gorm"
)

type AssetLinked struct {
	commonmodel.ModelV2
	ChildAssetID        string               `json:"child_asset_id" gorm:"type:varchar(40);not null"`
	ChildAsset          AssetTyre            `json:"child_asset"`
	ParentAsset         AssetVehicle         `json:"parent_asset"`
	AssetChild          Asset                `json:"asset_child" gorm:"foreignKey:ChildAssetID;references:ID"`
	AssetParent         Asset                `json:"asset_parent" gorm:"foreignKey:ParentAssetID;references:ID"`
	ParentAssetID       string               `json:"parent_asset_id" gorm:"type:varchar(40);not null"`
	LinkedDatetime      time.Time            `json:"linked_datetime" gorm:"not null"`
	UnlinkedDatetime    *time.Time           `json:"unlinked_datetime"`
	ClientID            string               `json:"client_id" gorm:"type:varchar(40);not null"`
	LinkedAssetTypeCode string               `json:"linked_asset_type_code" gorm:"type:varchar(20);not null"`
	LinkedAssetType     AssetLinkedAssetType `json:"linked_asset_type"`

	AssetLinkedAssetVehicleTyre *AssetLinkedAssetVehicleTyre
}

func (la *AssetLinked) TableName() string {
	return "ams_linked_assets"
}

func (la *AssetLinked) SetID() {
	la.SetUUID("las")
}

func (la *AssetLinked) BeforeCreate(db *gorm.DB) error {
	la.SetUUID("las")
	la.ModelV2.BeforeCreate(db)
	return nil
}

func (la *AssetLinked) BeforeUpdate(db *gorm.DB) error {
	la.ModelV2.BeforeUpdate(db)
	return nil
}

type TyreRotationReportModel struct {
	PrevTyrePosition int
	NewTyrePosition  int
	UnlinkedDatetime time.Time
	ChildAssetID     string
	ParentAssetID    string

	ChildAsset  AssetTyre
	AssetParent Asset `gorm:"foreignKey:ParentAssetID;references:ID"`
}

func (la *TyreRotationReportModel) TableName() string {
	return "ams_linked_assets"
}

type AssetLinkedWhere struct {
	ID                string
	IDs               []string
	ClientID          string
	ParentAssetID     string
	ChildAssetID      string
	WithUnlinked      bool
	TypeCode          string
	RelatedAssetID    string
	AssignedToUserIDs []string
	SerialNumber      string
	AssetName         string
	ReferenceNumber   string
}

type AssetLinkedCondition struct {
	Where       AssetLinkedWhere
	Columns     []string
	Preload     AssetLinkedPreload
	IsForUpdate bool
}

type AssetLinkedPreload struct {
	AssetLinkedAssetVehicleTyre bool
	ChildAsset                  bool
	ParentAsset                 bool
	AssetChild                  bool
	AssetParent                 bool
}

type GetAssetLinkedGeneralListParam struct {
	commonmodel.ListRequest
	Cond AssetLinkedCondition
}
