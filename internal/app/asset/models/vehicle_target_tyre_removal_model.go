package models

import (
	"assetfindr/pkg/common/commonmodel"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

// VehicleTargetTyreRemoval represents the ams_vehicle_target_tyre_removals table
type VehicleTargetTyreRemoval struct {
	commonmodel.ModelV2
	TargetRTD float64 `json:"target_rtd" gorm:"type:numeric(10,2);not null" pgjsonb:"target_rtd"`

	// Relationships
	AssetVehicles []AssetVehicle `json:"asset_vehicles" gorm:"foreignKey:TargetRemovalID;references:ID"`
}

// TableName specifies the database table name for the VehicleTargetTyreRemoval model
func (v *VehicleTargetTyreRemoval) TableName() string {
	return "ams_vehicle_target_tyre_removals"
}

func (v *VehicleTargetTyreRemoval) BeforeCreate(db *gorm.DB) error {
	v.SetUUID("vtr")
	v.ModelV2.BeforeCreate(db)
	return nil
}

func (v *VehicleTargetTyreRemoval) BeforeUpdate(db *gorm.DB) error {
	v.ModelV2.BeforeUpdate(db)
	return nil
}

// VehicleTargetTyreRemovalCondition defines query conditions for VehicleTargetTyreRemoval
type VehicleTargetTyreRemovalCondition struct {
	Where   VehicleTargetTyreRemovalWhere
	Preload VehicleTargetTyreRemovalPreload
	Columns []string
}

// VehicleTargetTyreRemovalWhere defines where conditions for VehicleTargetTyreRemoval queries
type VehicleTargetTyreRemovalWhere struct {
	ID        string
	ClientID  string
	TargetRTD null.Float
	AssetIDs  []string
}

// VehicleTargetTyreRemovalPreload defines preload options for VehicleTargetTyreRemoval queries
type VehicleTargetTyreRemovalPreload struct {
	AssetVehicles bool
}

// GetVehicleTargetTyreRemovalListParam defines parameters for listing VehicleTargetTyreRemovals
type GetVehicleTargetTyreRemovalListParam struct {
	commonmodel.ListRequest
	Cond VehicleTargetTyreRemovalCondition
}
