package models

import (
	"encoding/json"
	"math"
	"time"

	"cloud.google.com/go/bigquery"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type GeneralSensor struct {
	AssetID                    string     `json:"asset_id" gorm:"column:asset_id"`
	Time                       time.Time  `json:"time" gorm:"column:time"`
	Ident                      string     `json:"ident" gorm:"column:ident"`
	CreatedAt                  time.Time  `json:"created_at" gorm:"column:created_at"`
	IntegrationID              string     `json:"integration_id" gorm:"column:integration_id"`
	ClientID                   string     `json:"client_id" gorm:"column:client_id"`
	BatteryCurrent             null.Float `json:"battery_current" gorm:"column:battery_current"`
	BatteryVoltage             null.Float `json:"battery_voltage" gorm:"column:battery_voltage"`
	GsmSignalLevel             null.Int   `json:"gsm_signal_level" gorm:"column:gsm_signal_level"`
	GsmOperatorCode            null.Int   `json:"gsm_operator_code" gorm:"column:gsm_operator_code"`
	MovementStatus             null.Bool  `json:"movement_status" gorm:"column:movement_status"`
	ExternalPowersourceVoltage null.Float `json:"external_powersource_voltage" gorm:"column:external_powersource_voltage"`
	XAccelaration              null.Float `json:"x_acceleration" gorm:"column:x_acceleration"`
	YAccelaration              null.Float `json:"y_acceleration" gorm:"column:y_acceleration"`
	ZAccelaration              null.Float `json:"z_acceleration" gorm:"column:z_acceleration"`
	PtoDriveEngagementEnum     null.Int   `json:"pto_drive_engagement_enum" gorm:"column:pto_drive_engagement_enum"`
	FuelConsumed               null.Float `json:"fuel_consumed" gorm:"column:fuel_consumed"`
	SdStatus                   null.Bool  `json:"sd_status"  gorm:"column:sd_status"`
	DigitalInput               null.Bool  `json:"digital_input" gorm:"column:digital_input"`
	DigitalOutput              null.Bool  `json:"digital_output" gorm:"column:digital_output"`
	Pitch                      null.Float `json:"pitch" gorm:"column:pitch"`
	Roll                       null.Float `json:"roll" gorm:"column:roll"`
}

func (i *GeneralSensor) Save() (map[string]bigquery.Value, string, error) {
	var myMap map[string]bigquery.Value
	data, _ := json.Marshal(*i)
	json.Unmarshal(data, &myMap)
	return myMap, bigquery.NoDedupeID, nil
}

func (GeneralSensor) TableName() string {
	return "ssr_general_sensor_data"
}

func (m *GeneralSensor) BeforeCreate(tx *gorm.DB) error {
	if m.XAccelaration.Valid && (!m.Pitch.Valid && !m.Roll.Valid) {
		norm := math.Sqrt(m.XAccelaration.Float64*m.XAccelaration.Float64 + m.YAccelaration.Float64*m.YAccelaration.Float64 + m.ZAccelaration.Float64*m.ZAccelaration.Float64)
		m.Pitch = null.FloatFrom(math.Asin(m.XAccelaration.Float64/norm)*180/math.Pi - 46.66)
		m.Roll = null.FloatFrom(math.Atan2(m.ZAccelaration.Float64, m.YAccelaration.Float64)*180/math.Pi + 90.07)
	}
	return nil
}

type GeneralSensorDigitalInputHM struct {
	AssetID      string     `json:"asset_id" gorm:"column:asset_id"`
	Time         time.Time  `json:"time" gorm:"column:time"`
	DigitalInput null.Bool  `json:"digital_input" gorm:"column:digital_input"`
	TotalHM      null.Float `json:"total_hm" gorm:"column:total_hm"`
}

type GeneralSensorBQ struct {
	AssetID                    string               `bigquery:"asset_id"`
	Time                       time.Time            `bigquery:"time"`
	Ident                      string               `bigquery:"ident"`
	CreatedAt                  time.Time            `bigquery:"created_at"`
	IntegrationID              string               `bigquery:"integration_id"`
	ClientID                   string               `bigquery:"client_id"`
	BatteryCurrent             bigquery.NullFloat64 `bigquery:"battery_current"`
	BatteryVoltage             bigquery.NullFloat64 `bigquery:"battery_voltage"`
	GsmSignalLevel             bigquery.NullInt64   `bigquery:"gsm_signal_level"`
	GsmOperatorCode            bigquery.NullInt64   `bigquery:"gsm_operator_code"`
	MovementStatus             bigquery.NullBool    `bigquery:"movement_status"`
	SdStatus                   bigquery.NullBool    `bigquery:"sd_status"`
	ExternalPowersourceVoltage bigquery.NullFloat64 `bigquery:"external_powersource_voltage"`
	XAccelaration              bigquery.NullFloat64 `bigquery:"x_acceleration"`
	YAccelaration              bigquery.NullFloat64 `bigquery:"y_acceleration"`
	ZAccelaration              bigquery.NullFloat64 `bigquery:"z_acceleration"`
	PtoDriveEngagementEnum     bigquery.NullInt64   `bigquery:"pto_drive_engagement_enum"`
	FuelConsumed               bigquery.NullFloat64 `bigquery:"fuel_consumed"`
	DigitalInput               bigquery.NullBool    `bigquery:"digital_input"`
	DigitalOutput              bigquery.NullBool    `bigquery:"digital_output"`
}
