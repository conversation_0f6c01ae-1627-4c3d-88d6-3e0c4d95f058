package usecase

import (
	assetModels "assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/log/models"
	"assetfindr/pkg/common/commonmodel"
	"context"
)

func (uc *LogUseCase) checkKMHMData(prev map[string]interface{}, new map[string]interface{}) {
	if prev["Use Kilometer"] == new["Use Kilometer"] {
		delete(prev, "Use Kilometer")
		delete(new, "Use Kilometer")
	}
	if prev["Use Hourmeter"] == new["Use Hourmeter"] {
		delete(prev, "Use Hourmeter")
		delete(new, "Use Hourmeter")
	}
}

func (uc *LogUseCase) GetAssetVehicleLog(ctx context.Context, log *models.Log) (*commonmodel.DetailResponse, error) {
	prevVal := assetModels.AssetVehicle{}
	if !log.PreviousValue.Valid {
		log.PreviousValue.JSONVal = "{}"
	}

	err := json.Unmarshal([]byte(log.PreviousValue.JSONVal), &prevVal)
	if err != nil {
		return nil, err
	}

	newVal := assetModels.AssetVehicle{}
	if !log.NewValue.Valid {
		log.NewValue.JSONVal = "{}"
	}

	err = json.Unmarshal([]byte(log.NewValue.JSONVal), &newVal)
	if err != nil {
		return nil, err
	}

	prevData := map[string]interface{}{}
	prevValModel := convertModel(prevVal.ModelV2NoIDField)
	err = uc.PopulateModelV2Data(ctx, prevData, prevValModel)
	if err != nil {
		return nil, err
	}

	newData := map[string]interface{}{}
	newValModel := convertModel(newVal.ModelV2NoIDField)
	err = uc.PopulateModelV2Data(ctx, newData, newValModel)
	if err != nil {
		return nil, err
	}

	err = uc.PopulateAssetVehicleData(ctx, prevData, prevVal)
	if err != nil {
		return nil, err
	}

	err = uc.PopulateAssetVehicleData(ctx, newData, newVal)
	if err != nil {
		return nil, err
	}
	uc.checkKMHMData(prevData, newData)

	resp := commonmodel.Log{
		ID:            log.ID,
		TableName:     log.TableName,
		PreviousValue: prevData,
		NewValue:      newData,
		ClientID:      log.ClientID,
		CreatedAt:     log.CreatedAt,
		CreatedBy:     log.CreatedBy.String(),
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: log.ID,
		Data:        resp,
	}, nil
}

func (uc *LogUseCase) PopulateAssetVehicleData(ctx context.Context, data map[string]interface{}, model assetModels.AssetVehicle) error {
	populateValString(data, model.AssetID, "Asset ID")
	populateValString(data, model.RegistrationNumber, "Registration Number")
	populateValString(data, model.AssetVehicleBodyTypeID, "Vehicle Body Type ID")
	populateValInt(data, int(model.NumberOfTyres.Int64), "Number of Tyres")
	populateValString(data, model.EngineModel, "Engine Model")
	populateValString(data, model.TransmissionModel, "Transmission Model")
	populateValString(data, model.VrdNumber, "VRD Number")
	populateValTime(data, model.VrdExpiryDate, "VRD Expiry Date")
	populateValString(data, model.VrdNumberAssignTo, "VRD Number Assign To")
	populateValString(data, model.EngineNumber, "Engine Number")
	populateValString(data, model.ChassisNumber, "Chassis Number")
	populateValSqlString(data, &model.GpsDeviceImei, "GPS Device IMEI")
	populateValString(data, model.RegistrationCertificateNumber, "Registration Certificate Number")
	populateValString(data, model.RegistrationCertificateAssignTo, "Registration Certificate Assign To")
	populateValString(data, model.InspectionBookNumber, "Inspection Book Number")
	populateValString(data, model.InspectionBookNumberAssignTo, "Inspection Book Number Assign To")
	populateValTime(data, model.InspectionBookExpiryDate, "Inspection Book Expiry Date")
	populateValFloat64(data, model.VehicleKM, "Vehicle KM")
	populateValInt(data, model.VehicleHm, "Vehicle HM")
	populateValSqlInt64(data, &model.NumberOfSpareTyres.NullInt64, "Number of Spare Tyres")
	populateValBool(data, model.UseKilometer.Bool, "Use Kilometer")
	populateValBool(data, model.UseHourmeter.Bool, "Use Hourmeter")
	uc.populateValAsset(ctx, data, model.AssetID)
	return nil
}
