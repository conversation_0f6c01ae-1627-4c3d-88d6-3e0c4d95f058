package dtos

import (
	assetsModels "assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/task/models"
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
)

type CreateUpdateTask struct {
	Subject          string      `json:"subject"`
	Description      null.String `json:"description"`
	StatusCode       string      `json:"status_code"`
	ScheduleDatetime null.Time   `json:"schedule_datetime"`
	AssetID          string      `json:"asset_id"`
}

type ScheduleTask struct {
	ScheduleDatetime null.Time `json:"schedule_datetime"`
}

type TaskListItem struct {
	ID                    string      `json:"id"`
	TaskNumber            string      `json:"task_number"`
	Subject               string      `json:"subject"`
	Description           null.String `json:"description"`
	StatusCode            string      `json:"status_code"`
	ScheduleDatetime      null.Time   `json:"schedule_datetime"`
	AssetID               string      `json:"asset_id"`
	AssetReferenceNumber  string      `json:"asset_reference_number"`
	AssetSerialNumber     string      `json:"asset_serial_number"`
	AssetPartnerOwnerName string      `json:"asset_partner_owner_name"`
	CreatedBy             string      `json:"created_by"`
	CreatedAt             time.Time   `json:"created_at"`
	UpdatedAt             time.Time   `json:"updated_at"`
}

func BuildTaskListRespItem(task models.Task, asset *assetsModels.Asset) TaskListItem {
	item := TaskListItem{
		ID:               task.ID,
		TaskNumber:       task.TaskNumber,
		Subject:          task.Subject,
		Description:      task.Description,
		StatusCode:       task.StatusCode,
		ScheduleDatetime: task.ScheduleDatetime,
		AssetID:          task.AssetID,
		CreatedBy:        task.CreatedBy,
		CreatedAt:        task.CreatedAt,
		UpdatedAt:        task.UpdatedAt,
	}

	if asset != nil {
		item.AssetReferenceNumber = asset.ReferenceNumber
		item.AssetSerialNumber = asset.SerialNumber
		item.AssetPartnerOwnerName = asset.PartnerOwnerName
	}

	return item
}

type TaskDetail struct {
	ID               string      `json:"id"`
	TaskNumber       string      `json:"task_number"`
	Subject          string      `json:"subject"`
	Description      null.String `json:"description"`
	StatusCode       string      `json:"status_code"`
	ScheduleDatetime null.Time   `json:"schedule_datetime"`
	AssetID          string      `json:"asset_id"`
	CreatedBy        string      `json:"created_by"`
	CreatedAt        time.Time   `json:"created_at"`
	UpdatedAt        time.Time   `json:"updated_at"`
	Asset            *TaskAsset  `json:"asset"`
}

func BuildTaskDetailResp(task models.Task, asset *assetsModels.Asset) TaskDetail {
	resp := TaskDetail{
		ID:               task.ID,
		TaskNumber:       task.TaskNumber,
		Subject:          task.Subject,
		Description:      task.Description,
		StatusCode:       task.StatusCode,
		ScheduleDatetime: task.ScheduleDatetime,
		AssetID:          task.AssetID,
		CreatedBy:        task.CreatedBy,
		CreatedAt:        task.CreatedAt,
		UpdatedAt:        task.UpdatedAt,
	}
	if asset != nil {
		resp.Asset = &TaskAsset{
			ID:                     asset.ID,
			Name:                   asset.Name,
			Photo:                  asset.Photo,
			BrandID:                asset.BrandID,
			BrandName:              asset.Brand.BrandName,
			SerialNumber:           asset.SerialNumber,
			ReferenceNumber:        asset.ReferenceNumber,
			CategoryCode:           asset.AssetCategoryCode,
			PartnerOwnerName:       asset.PartnerOwnerName,
			PartnerOwnerID:         asset.PartnerOwnerID,
			IsWorkshop:             asset.IsWorkshop,
			CustomAssetCategory:    BuildCustomAssetCategory(asset.CustomAssetCategory),
			CustomAssetSubCategory: BuildCustomAssetSubCategory(asset.CustomAssetSubCategory),
			AssetTyre:              BuildAssetTyre(asset.AssetTyre),
		}
	}
	return resp

}

type TaskAsset struct {
	ID                     string                  `json:"id"`
	Name                   string                  `json:"name"`
	Photo                  null.String             `json:"photo"`
	BrandID                string                  `json:"brand_id"`
	BrandName              string                  `json:"brand_name"`
	SerialNumber           string                  `json:"serial_number"`
	ReferenceNumber        string                  `json:"reference_number"`
	CategoryCode           string                  `json:"category_code"`
	PartnerOwnerName       string                  `json:"partner_owner_name"`
	PartnerOwnerID         string                  `json:"partner_owner_id"`
	IsWorkshop             bool                    `json:"is_workshop"`
	CustomAssetCategory    *CustomAssetCategory    `json:"custom_asset_category"`
	CustomAssetSubCategory *CustomAssetSubCategory `json:"custom_asset_sub_category"`
	AssetTyre              *AssetTyre              `json:"asset_tyre"`
}

type CustomAssetCategory struct {
	ID                string `json:"id"`
	Name              string `json:"name"`
	AssetCategoryCode string `json:"asset_category_code"`
	Description       string `json:"description"`
}

func BuildCustomAssetCategory(asset assetsModels.CustomAssetCategory) *CustomAssetCategory {
	if asset.ID == "" {
		return nil
	}

	return &CustomAssetCategory{
		ID:                asset.ID,
		Name:              asset.Name,
		AssetCategoryCode: asset.AssetCategoryCode,
		Description:       asset.Description,
	}
}

func BuildCustomAssetSubCategory(asset assetsModels.CustomAssetSubCategory) *CustomAssetSubCategory {
	if asset.ID == "" {
		return nil
	}
	return &CustomAssetSubCategory{
		ID:          asset.ID,
		Name:        asset.Name,
		Description: asset.Description,
	}
}

type CustomAssetSubCategory struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

type Tyre struct {
	ID                 string    `json:"id"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	BrandID            string    `json:"brand_id"`
	PatternType        string    `json:"pattern_type"`
	OriginalTd         float64   `json:"original_td"`
	ConstructionType   string    `json:"construction_type"`
	PlyRating          float64   `json:"ply_rating"`
	LoadRating         string    `json:"load_rating"`
	SpeedIndex         string    `json:"speed_index"`
	StarRating         float64   `json:"star_rating"`
	TraCode            string    `json:"tra_code"`
	ClientID           string    `json:"client_id"`
	SectionWidth       string    `json:"section_width"`
	Construction       string    `json:"construction"`
	RimDiameter        string    `json:"rim_diameter"`
	RecommendedRimSize string    `json:"recommended_rim_size"`
}

func BuildTyreResp(tyreModel assetsModels.Tyre) Tyre {
	return Tyre{
		ID:                 tyreModel.ID,
		CreatedAt:          tyreModel.CreatedAt,
		UpdatedAt:          tyreModel.UpdatedAt,
		BrandID:            tyreModel.BrandID,
		PatternType:        tyreModel.PatternType,
		OriginalTd:         tyreModel.OriginalTd,
		ConstructionType:   tyreModel.ConstructionType,
		PlyRating:          tyreModel.PlyRating,
		LoadRating:         tyreModel.LoadRating,
		SpeedIndex:         tyreModel.SpeedIndex,
		StarRating:         tyreModel.StarRating,
		TraCode:            tyreModel.TRACode,
		ClientID:           tyreModel.ClientID,
		SectionWidth:       tyreModel.SectionWidth,
		Construction:       tyreModel.Construction,
		RimDiameter:        tyreModel.RimDiameter,
		RecommendedRimSize: tyreModel.RecommendedRimSize,
	}
}

type AssetTyre struct {
	RetreadNumber              int     `json:"retread_number"`
	DOTCode                    string  `json:"dot_code"`
	AverageRTD                 float64 `json:"average_rtd"`
	StartThreadDepth           float64 `json:"start_thread_depth"`
	ParentAssetReferenceNumber string  `json:"parent_asset_reference_number"`
	ParentAssetSerialNumber    string  `json:"parent_asset_serial_number"`
	ParentAssetName            string  `json:"parent_asset_name"`
	Tyre                       Tyre    `json:"tyre"`
}

func BuildAssetTyre(assetTyreModel *assetsModels.AssetTyre) *AssetTyre {
	if assetTyreModel == nil {
		return nil
	}

	item := &AssetTyre{
		DOTCode:          assetTyreModel.DOTCode.String,
		RetreadNumber:    assetTyreModel.RetreadNumber,
		AverageRTD:       assetTyreModel.AverageRTD,
		StartThreadDepth: assetTyreModel.StartThreadDepth,
		Tyre:             BuildTyreResp(assetTyreModel.Tyre),
	}

	if assetTyreModel.AssetLinked != nil {
		item.ParentAssetReferenceNumber = assetTyreModel.AssetLinked.AssetParent.ReferenceNumber
		item.ParentAssetSerialNumber = assetTyreModel.AssetLinked.AssetParent.SerialNumber
		item.ParentAssetName = assetTyreModel.AssetLinked.AssetParent.Name
	}

	return item
}

type TaskListReq struct {
	commonmodel.ListRequest
	StatusCodes []string  `form:"status_codes"`
	AssetIDs    []string  `form:"asset_ids"`
	StartDate   time.Time `form:"schedule_start_time"`
	EndDate     time.Time `form:"schedule_end_time"`
}
