package dtos

import (
	"assetfindr/internal/app/asset/dtos"
	"encoding/json"

	assetServiceModels "assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/task/models"

	userIdentityModel "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/sqlhelpers"
	"database/sql"
	"time"

	"gopkg.in/guregu/null.v4"
)

type TicketResponse struct {
	ID                  string                   `json:"id"`
	TicketNumber        string                   `json:"ticket_number"`
	TicketUUID          string                   `json:"ticket_id"`
	Subject             string                   `json:"subject"`
	Description         string                   `json:"description"`
	TicketCategoryCode  string                   `json:"ticket_category_code"`
	TicketReferenceCode string                   `json:"ticket_reference_code"`
	ReferenceID         string                   `json:"reference_id"`
	Asset               assetServiceModels.Asset `json:"asset"`
	SeverityLevel       models.SeverityLevel     `json:"severity_level"`
	SeverityLevelCode   string                   `json:"severity_level_code"`
	RequesterUserID     string                   `json:"requester_user_id"`
	AssignedToUserID    null.String              `json:"assigned_to_user_id"`
	RequesterUser       userIdentityModel.User   `json:"requester_user"`
	AssignedToUser      userIdentityModel.User   `json:"assigned_to_user"`
	StatusCode          string                   `json:"status_code"`
	Resolution          string                   `json:"resolution"`
	Cost                null.Int                 `json:"cost"`
	ClientID            string                   `json:"client_id"`
	CreatedAt           time.Time                `json:"created_at"`
	UpdatedAt           time.Time                `json:"updated_at"`
	ParentTicketID      string                   `json:"parent_ticket_id"`
	LinkedTicketsCount  int                      `json:"linked_tickets_count"`
	RunningTime         int                      `json:"running_time"`
	StartTime           *sql.NullTime            `json:"start_time"`
	DueDatetime         string                   `json:"due_datetime"`
	ScheduleDatetime    string                   `json:"schedule_datetime"`
	PartnerOwnerID      string                   `json:"partner_owner_id"`
	PartnerOwnerNo      string                   `json:"partner_owner_no"`
	PartnerOwnerName    string                   `json:"partner_owner_name"`

	Contacts []UpsertTicketContact `json:"contacts,omitempty"`

	Photos []commonmodel.PhotoReq `json:"photos"`
}

type ListTicketResponse struct {
	ID                  string                   `json:"id"`
	TicketNumber        string                   `json:"ticket_number"`
	TicketUUID          string                   `json:"ticket_id"`
	Subject             string                   `json:"subject"`
	Description         string                   `json:"description"`
	TicketCategoryCode  string                   `json:"ticket_category_code"`
	TicketCategory      models.TicketCategory    `json:"ticket_category"`
	TicketReferenceCode string                   `json:"ticket_reference_code"`
	ReferenceID         string                   `json:"reference_id"`
	Asset               assetServiceModels.Asset `json:"asset"`
	SeverityLevel       models.SeverityLevel     `json:"severity_level"`
	SeverityLevelCode   string                   `json:"severity_level_code"`
	RequesterUserID     string                   `json:"requester_user_id"`
	AssignedToUserID    string                   `json:"assigned_to_user_id"`
	RequesterUser       userIdentityModel.User   `json:"requester_user"`
	AssignedToUser      userIdentityModel.User   `json:"assigned_to_user"`
	StatusCode          string                   `json:"status_code"`
	Resolution          string                   `json:"resolution"`
	Cost                null.Int                 `json:"cost"`
	ClientID            string                   `json:"client_id"`
	CreatedAt           time.Time                `json:"created_at"`
	UpdatedAt           time.Time                `json:"updated_at"`
	ParentTicketID      string                   `json:"parent_ticket_id"`
	LinkedTicketsCount  int                      `json:"linked_tickets_count"`
	RunningTime         int                      `json:"running_time"`
	StartTime           *sqlhelpers.SqlNullTime  `json:"start_time"`
	DueDatetime         *time.Time               `json:"due_datetime"`
	ScheduleDatetime    *time.Time               `json:"schedule_datetime"`
	ScheduleDatetimeEnd *time.Time               `json:"schedule_datetime_end"`
	DepartmentID        string                   `json:"department_id"`
	DepartmentName      string                   `json:"department_name"`
	IsArchived          bool                     `json:"is_archived"`

	Photos []commonmodel.PhotoReq `json:"photos"`
}

func BuildTksTicketResponse(
	tickets []models.Ticket,
	usersMapById map[string]userIdentityModel.User,
	assetsMapById map[string]assetServiceModels.Asset,
	departmentsMapById map[string]userIdentityModel.Department,
) []ListTicketResponse {
	response := []ListTicketResponse{}

	for _, ticket := range tickets {
		requesterUserID := ticket.RequesterUserID
		requesterUser, _ := usersMapById[requesterUserID]

		assignedToUserID := ticket.AssignedToUserID
		assignedToUser, _ := usersMapById[assignedToUserID.String]

		referenceID := ticket.ReferenceID
		var asset assetServiceModels.Asset
		asset, _ = assetsMapById[referenceID]
		var duedatetime *time.Time
		if ticket.DueDatetime == nil {
			duedatetime = nil
		} else {
			duedatetime = ticket.DueDatetime
		}

		var scheduletime *time.Time
		if ticket.ScheduleDatetime == nil {
			scheduletime = nil
		} else {
			scheduletime = ticket.ScheduleDatetime
		}

		response = append(response, ListTicketResponse{
			ID:                  ticket.ID,
			TicketNumber:        ticket.TicketNumber,
			Subject:             ticket.Subject,
			Description:         ticket.Description,
			TicketCategoryCode:  ticket.TicketCategoryCode,
			TicketCategory:      ticket.TicketCategory,
			TicketReferenceCode: ticket.TicketReferenceCode,
			Asset:               asset,
			ReferenceID:         ticket.ReferenceID,
			SeverityLevelCode:   ticket.SeverityLevelCode,
			SeverityLevel:       ticket.SeverityLevel,
			RequesterUserID:     ticket.RequesterUserID,
			Resolution:          ticket.Resolution,
			Cost:                ticket.Cost,
			RequesterUser:       requesterUser,
			AssignedToUserID:    ticket.AssignedToUserID.String,
			AssignedToUser:      assignedToUser,
			StatusCode:          ticket.StatusCode,
			ClientID:            ticket.ClientID,
			CreatedAt:           ticket.CreatedAt,
			UpdatedAt:           ticket.UpdatedAt,
			LinkedTicketsCount:  ticket.CountLinkedTickets(),
			RunningTime:         ticket.RunningTime,
			StartTime:           ticket.StartTime,
			DueDatetime:         duedatetime,
			ScheduleDatetime:    scheduletime,
			ScheduleDatetimeEnd: ticket.ScheduleDatetimeEnd,
			DepartmentID:        ticket.DepartmentID,
			DepartmentName:      departmentsMapById[ticket.DepartmentID].Name,
			IsArchived:          ticket.IsArchived,
		})
	}

	return response
}

type TicketListReq struct {
	commonmodel.ListRequest
	ReferenceID               string   `form:"reference_id"`
	StatusCodes               []string `form:"status_codes"`
	Subjects                  []string `form:"subjects"`
	AssignedToMe              bool     `form:"assigned_to_me"`
	CreatedByMe               bool     `form:"created_by_me"`
	StartDate                 string   `form:"schedule_start_datetime"`
	EndDate                   string   `form:"schedule_end_datetime"`
	DepartmentID              string   `form:"department_id"`
	PartnerOwnerID            string   `form:"partner_owner_id"`
	Categories                []string `form:"categories"`
	ExcludeCategories         []string `form:"exclude_categories"`
	HasCost                   bool     `form:"has_cost"`
	IsArchived                bool     `form:"is_archived"`
	IsOverDue                 bool     `form:"is_overdue"`
	IsDueSoon                 bool     `form:"is_due_soon"`
	IsUpComingServiceReminder bool     `form:"is_up_coming_service_reminder"`
	IsOverdueServiceReminder  bool     `form:"is_overdue_service_reminder"`
	RequesterUserIDs          []string `form:"requester_user_ids"`
	RequestedByUserIDs        []string `form:"requested_by_user_ids"`
	AssignedToUserIDs         []string `form:"assigned_to_user_ids"`
}

type TicketListV2Req struct {
	commonmodel.ListRequest
	// Filter
	StatusCodes        []string `form:"status_codes"`
	StartDueDate       string   `form:"start_due_datetime"`
	EndDueDate         string   `form:"end_due_datetime"`
	Categories         []string `form:"categories"`
	SeverityLevelCodes []string `form:"severity_level_codes"`
	ReferenceIDs       []string `form:"reference_ids"`
	DepartmentIDs      []string `form:"department_ids"`
	CreatedByUserIDs   []string `form:"created_by_user_ids"`
	AssignedToUserIDs  []string `form:"assigned_to_user_ids"`

	// Quick Filter
	IsOverDue                 bool `form:"is_overdue"`
	IsDueSoon                 bool `form:"is_due_soon"`
	IsUpComingServiceReminder bool `form:"is_up_coming_service_reminder"`
	AssignedToMe              bool `form:"assigned_to_me"`
	IsRelatedToMyAssets       bool `form:"is_related_to_my_assets"`
	CreatedByMe               bool `form:"created_by_me"`
	IsOpen                    bool `form:"is_open"`
	IsRelatedToMyDepartments  bool `form:"is_related_to_my_departments"`

	// Other Filter
	ReferenceID              string   `form:"reference_id"`
	StartDate                string   `form:"schedule_start_datetime"`
	EndDate                  string   `form:"schedule_end_datetime"`
	ExcludeCategories        []string `form:"exclude_categories"`
	HasCost                  bool     `form:"has_cost"`
	IsArchived               bool     `form:"is_archived"`
	IsOverdueServiceReminder bool     `form:"is_overdue_service_reminder"`
	PartnerOwnerID           string   `form:"partner_owner_id"`
}

func (r *TicketListReq) Validate() error {
	r.Normalize()
	return nil
}

type TicketDetailResponse struct {
	ID                  string                   `json:"id"`
	TicketNumber        string                   `json:"ticket_number"`
	TicketUUID          string                   `json:"ticket_id"`
	Subject             string                   `json:"subject"`
	Description         string                   `json:"description"`
	TicketCategoryCode  string                   `json:"ticket_category_code"`
	TicketCategory      models.TicketCategory    `json:"ticket_category"`
	TicketReferenceCode string                   `json:"ticket_reference_code"`
	Asset               assetServiceModels.Asset `json:"asset"`
	ReferenceID         string                   `json:"reference_id"`
	SeverityLevelCode   string                   `json:"severity_level_code"`
	SeverityLevel       models.SeverityLevel     `json:"severity_level"`
	RequesterUserID     string                   `json:"requester_user_id"`
	AssignedToUserID    string                   `json:"assigned_to_user_id"`
	RequesterUser       userIdentityModel.User   `json:"requester_user"`
	AssignedToUser      userIdentityModel.User   `json:"assigned_to_user"`
	StatusCode          string                   `json:"status_code"`
	Resolution          string                   `json:"resolution"`
	Cost                null.Int                 `json:"cost"`
	ClientID            string                   `json:"client_id"`
	CreatedAt           time.Time                `json:"created_at"`
	UpdatedAt           time.Time                `json:"updated_at"`
	Photos              []commonmodel.PhotoReq   `json:"photos"`
	RunningTime         int                      `json:"running_time"`
	StartTime           *sqlhelpers.SqlNullTime  `json:"start_time"`
	DueDatetime         *time.Time               `json:"due_datetime"`
	ScheduleDatetime    *time.Time               `json:"schedule_datetime"`
	ScheduleDatetimeEnd *time.Time               `json:"schedule_datetime_end"`
	DepartmentID        string                   `json:"department_id"`
	DepartmentName      string                   `json:"department_name"`

	LocationID   string `json:"location_id"`
	LocationName string `json:"location_name"`
	IsArchived   bool   `json:"is_archived"`

	ResolutionNote string `json:"resolution_note"`
	ResolutionBy   string `json:"resolution_by"`

	AssetDataInformation AssetDataInformation `json:"asset_detail_information"`
}

func BuildTicketDetailResponse(
	ticket *models.Ticket,
	usersMapById map[string]userIdentityModel.User,
	asset assetServiceModels.Asset,
	departmentName string,
) TicketDetailResponse {
	requesterUserID := ticket.RequesterUserID
	requesterUser, _ := usersMapById[requesterUserID]

	assignedToUserID := ticket.AssignedToUserID
	assignedToUser, _ := usersMapById[assignedToUserID.String]

	var duedatetime *time.Time
	if ticket.DueDatetime == nil {
		duedatetime = nil
	} else {
		duedatetime = ticket.DueDatetime
	}

	var scheduletime *time.Time
	if ticket.ScheduleDatetime == nil {
		scheduletime = nil
	} else {
		scheduletime = ticket.ScheduleDatetime
	}

	if asset.Photo.String != "" {
		photoUrl, _ := helpers.GenerateCloudStorageSignedURL(asset.Photo.String, time.Duration(24))
		asset.Photo = null.StringFrom(photoUrl)
	}

	assetDataInformation := AssetDataInformation{}
	json.Unmarshal(ticket.AssetDataInformation.Bytes, &assetDataInformation)

	response := TicketDetailResponse{
		ID:                   ticket.ID,
		TicketNumber:         ticket.TicketNumber,
		Subject:              ticket.Subject,
		Description:          ticket.Description,
		TicketCategoryCode:   ticket.TicketCategoryCode,
		TicketCategory:       ticket.TicketCategory,
		TicketReferenceCode:  ticket.TicketReferenceCode,
		Asset:                asset,
		ReferenceID:          ticket.ReferenceID,
		SeverityLevelCode:    ticket.SeverityLevelCode,
		SeverityLevel:        ticket.SeverityLevel,
		RequesterUserID:      ticket.RequesterUserID,
		RequesterUser:        requesterUser,
		AssignedToUserID:     ticket.AssignedToUserID.String,
		AssignedToUser:       assignedToUser,
		StatusCode:           ticket.StatusCode,
		Resolution:           ticket.Resolution,
		Cost:                 ticket.Cost,
		ClientID:             ticket.ClientID,
		CreatedAt:            ticket.CreatedAt,
		UpdatedAt:            ticket.UpdatedAt,
		RunningTime:          ticket.RunningTime,
		StartTime:            ticket.StartTime,
		DueDatetime:          duedatetime,
		ScheduleDatetime:     scheduletime,
		ScheduleDatetimeEnd:  ticket.ScheduleDatetimeEnd,
		DepartmentID:         ticket.DepartmentID,
		DepartmentName:       departmentName,
		LocationID:           asset.LocationID,
		LocationName:         asset.Location.Name,
		IsArchived:           ticket.IsArchived,
		ResolutionNote:       ticket.ResolutionNote,
		ResolutionBy:         usersMapById[ticket.ResolutionBy].GetName(),
		AssetDataInformation: assetDataInformation,
	}

	return response
}

type TicketItemResp struct {
	ID                 string                `json:"id"`
	TicketNumber       string                `json:"ticket_number"`
	Subject            string                `json:"subject"`
	TicketCategoryCode string                `json:"ticket_category_code"`
	TicketCategory     models.TicketCategory `json:"ticket_category"`

	Description         string    `json:"description"`
	CreatedAt           time.Time `json:"created_at"`
	TicketReferenceCode string    `json:"ticket_reference_code"`
	ReferenceID         string    `json:"reference_id"`
	Asset               Asset     `json:"asset"`
	RequesterUserID     string    `json:"requester_user_id"`
	AssignedToUserID    string    `json:"assigned_to_user_id"`
	RequesterUser       User      `json:"requester_user"`
	AssignedToUser      User      `json:"assigned_to_user"`
	SeverityLevelCode   string    `json:"severity_level_code"`
	StatusCode          string    `json:"status_code"`
}

type Asset struct {
	ID                string `json:"id"`
	Name              string `json:"name"`
	AssetCategoryCode string `json:"asset_category_code"`
}

type User struct {
	ID        string `json:"id"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

type TicketCountResp struct {
	Open                    int `json:"open"`
	Overdue                 int `json:"overdue"`
	DueSoon                 int `json:"due_soon"`
	HighPriority            int `json:"high_priority"`
	ResolvedIn7Days         int `json:"resolved_in_7_days"`
	CreatedByMe             int `json:"created_by_me"`
	AssetOverdueWorkOrder   int `json:"asset_overdue_work_order"`
	UpcomingServiceReminder int `json:"upcoming_service_reminder"`
	OverdueServiceReminder  int `json:"overdue_service_reminder"`
}

type CreateTicketReqV2 struct {
	Subject             string      `json:"subject"`
	TicketCategoryCode  string      `json:"ticket_category_code"`
	Description         string      `json:"description"`
	TicketReferenceCode string      `json:"ticket_reference_code"`
	ReferenceID         string      `json:"reference_id"`
	SeverityLevelCode   string      `json:"severity_level_code"`
	DepartmentID        string      `json:"department_id"`
	LocationID          string      `json:"location_id"`
	AssignedToUserID    null.String `json:"assigned_to_user_id"`
	DueDatetime         null.Time   `json:"due_datetime"`
	ScheduleDatetime    null.Time   `json:"schedule_datetime"`
	ScheduleDatetimeEnd null.Time   `json:"schedule_datetime_end"`
	RequesterUserID     string      `json:"requester_user_id"`

	Photos []commonmodel.PhotoReq `json:"photos"`
}

type UpdateTicketReqV2 struct {
	Subject             string    `json:"subject"`
	TicketCategoryCode  string    `json:"ticket_category_code"`
	Description         string    `json:"description"`
	DueDatetime         null.Time `json:"due_datetime"`
	ScheduleDatetime    null.Time `json:"schedule_datetime"`
	ScheduleDatetimeEnd null.Time `json:"schedule_datetime_end"`
	RequesterUserID     string    `json:"requester_user_id"`

	Photos []commonmodel.PhotoReq `json:"photos"`
}

type UpdateTicketDueDateReq struct {
	DueDatetime null.Time `json:"due_datetime"`
}

// Will clean up code later
type AssetDataInformation struct {
	AssetID           string `json:"asset_id"`
	AssetCategoryCode string `json:"asset_category_code"` // TYRE / VEHICLE / OTHERS
	AssetName         string `json:"asset_name"`
	Brand             string `json:"brand_name"`
	PlateNumber       string `json:"plat_number"`
	SerialNumber      string `json:"serial_number"`
	AssetType         string `json:"category"`
	Category          string `json:"custom_category"`
	Subcategory       string `json:"custom_sub_category"`
	Location          string `json:"location_name"`
	Photo             string `json:"photo"`

	RetreadNumber             int                     `json:"retread_number"`
	PatternType               string                  `json:"pattern_type"`
	SectionWidth              string                  `json:"section_width"`
	ConstructionType          string                  `json:"construction"`
	RimDiameter               string                  `json:"rim_diameter"`
	DOTCode                   string                  `json:"dot_code"`
	PlyRating                 float64                 `json:"ply_rating"`
	StartTreadDepth           null.Float              `json:"start_thread_depth"`
	VehicleLinkage            dtos.AssetVehicleLinked `json:"asset_vehicle_linked"`
	LatestRemainingTreadDepth null.Float              `json:"latest_remaining_tread_depth"`
}

func (ai *AssetDataInformation) BuildAssetDataInformtion(
	asset *assetServiceModels.Asset,
	assetVehicleLink *dtos.AssetVehicleLinked) {

	ai.AssetID = asset.ID
	ai.AssetCategoryCode = asset.AssetCategoryCode
	ai.AssetName = asset.Name
	ai.Brand = asset.Brand.BrandName
	ai.PlateNumber = asset.ReferenceNumber
	ai.SerialNumber = asset.SerialNumber
	ai.AssetType = asset.AssetCategory.Label
	ai.Category = asset.CustomAssetCategory.Name
	ai.Subcategory = asset.CustomAssetSubCategory.Name
	ai.Location = asset.Location.Name
	ai.Photo = asset.Photo.String

	if asset.AssetTyre != nil {
		ai.RetreadNumber = asset.AssetTyre.RetreadNumber
		ai.PatternType = asset.AssetTyre.Tyre.PatternType
		ai.SectionWidth = asset.AssetTyre.Tyre.SectionWidth
		ai.ConstructionType = asset.AssetTyre.Tyre.Construction
		ai.RimDiameter = asset.AssetTyre.Tyre.RimDiameter
		ai.DOTCode = asset.AssetTyre.DOTCode.String
		ai.PlyRating = asset.AssetTyre.Tyre.PlyRating
		ai.StartTreadDepth = null.FloatFrom(asset.AssetTyre.StartThreadDepth)
		ai.LatestRemainingTreadDepth = null.FloatFrom(asset.AssetTyre.AverageRTD)
		if asset.AssetTyre.HasNotSetRTD.Bool {
			ai.LatestRemainingTreadDepth = null.Float{}
			if asset.AssetTyre.PrevKmHmDataUnavailable.Bool {
				ai.StartTreadDepth = null.Float{}
			}
		}
	}

	if assetVehicleLink != nil {
		ai.VehicleLinkage = *assetVehicleLink
	}
}
